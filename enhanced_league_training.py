#!/usr/bin/env python3
"""
增强版联赛模型训练系统
1. 动态获取所有联赛数据
2. 从odds表动态读取盘口线
3. 智能创建模型，小数据量也训练但标记为备用
"""

import pandas as pd
import numpy as np
import logging
import os
import pymysql
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Set

# 导入自定义模块
from models.data_processor import FootballDataProcessor
from models.goals_model import GoalsModel

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("EnhancedLeagueTraining")

class EnhancedLeagueTrainer:
    """增强版联赛训练器"""
    
    def __init__(self):
        self.processor = FootballDataProcessor(DB_CONFIG)
        self.training_results = {}
        
    def get_all_leagues_with_data(self, days_back: int = 1095) -> pd.DataFrame:
        """获取所有有数据的联赛"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        with self.processor.get_connection() as conn:
            query = """
            SELECT 
                l.id as league_id,
                l.name as league_name,
                l.english_name,
                l.priority,
                l.country,
                COUNT(DISTINCT m.id) as total_matches,
                COUNT(DISTINCT ms.match_id) as matches_with_stats,
                COUNT(DISTINCT o.match_id) as matches_with_odds,
                MIN(m.match_date) as earliest_match,
                MAX(m.match_date) as latest_match,
                CASE 
                    WHEN l.priority = 0 THEN '五大联赛'
                    WHEN l.priority = 1 THEN '一级赛事'
                    WHEN l.priority = 99 THEN '杯赛'
                    ELSE '其他联赛'
                END as tier_name
            FROM leagues l
            JOIN matches m ON l.id = m.league_id
            LEFT JOIN match_stats ms ON m.id = ms.match_id AND ms.total_goals IS NOT NULL
            LEFT JOIN odds o ON m.id = o.match_id
            WHERE m.match_date >= %s AND m.match_date <= %s
            GROUP BY l.id, l.name, l.english_name, l.priority, l.country
            HAVING total_matches >= 20  -- 至少20场比赛
            ORDER BY l.priority, matches_with_stats DESC
            """
            
            df = pd.read_sql(query, conn, params=[start_date, end_date])
        
        logger.info(f"发现 {len(df)} 个有数据的联赛")
        return df
    
    def get_available_handicap_lines(self, league_id: int, days_back: int = 1095) -> Set[float]:
        """获取特定联赛的可用盘口线"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        with self.processor.get_connection() as conn:
            query = """
            SELECT DISTINCT
                o.goals_line_1
            FROM odds o
            JOIN matches m ON o.match_id = m.id
            WHERE m.league_id = %s
            AND m.match_date >= %s
            AND m.match_date <= %s
            AND o.odds_type = 'OPENING'
            AND o.goals_line_1 IS NOT NULL
            """
            
            df = pd.read_sql(query, conn, params=[league_id, start_date, end_date])
        
        # 收集所有非空的盘口线 (只使用goals_line_1，因为其他字段没有数据)
        handicap_lines = set()
        if 'goals_line_1' in df.columns:
            lines = df['goals_line_1'].dropna().unique()
            handicap_lines.update(lines)
        
        # 过滤合理的盘口线范围
        valid_lines = {line for line in handicap_lines if 0.5 <= line <= 5.5}
        
        logger.debug(f"联赛 {league_id} 可用盘口线: {sorted(valid_lines)}")
        return valid_lines
    
    def get_league_match_data(self, league_id: int, days_back: int = 1095) -> pd.DataFrame:
        """获取特定联赛的比赛数据"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        with self.processor.get_connection() as conn:
            query = """
            SELECT 
                m.id as match_id,
                m.league_id,
                l.name as league_name,
                l.priority as league_priority,
                m.home_team_name,
                m.away_team_name,
                m.match_date,
                m.status,
                
                -- 比赛统计
                ms.home_goals, ms.away_goals, ms.total_goals,
                ms.home_corners, ms.away_corners, ms.total_corners,
                ms.home_yellow_cards, ms.away_yellow_cards, ms.total_yellow_cards,
                ms.home_shots, ms.away_shots, ms.total_shots,
                ms.home_shots_on_target, ms.away_shots_on_target, ms.total_shots_on_target,
                ms.home_possession, ms.away_possession,
                ms.home_attacks, ms.away_attacks, ms.total_attacks,
                ms.home_dangerous_attacks, ms.away_dangerous_attacks,
                
                -- 结果标签
                CASE 
                    WHEN ms.home_goals > ms.away_goals THEN 'H'
                    WHEN ms.home_goals < ms.away_goals THEN 'A'
                    ELSE 'D'
                END as result
                
            FROM matches m
            JOIN leagues l ON m.league_id = l.id
            JOIN match_stats ms ON m.id = ms.match_id
            WHERE m.league_id = %s
            AND m.match_date >= %s 
            AND m.match_date <= %s
            AND ms.total_goals IS NOT NULL
            ORDER BY m.match_date DESC
            """
            
            df = pd.read_sql(query, conn, params=[league_id, start_date, end_date])
        
        return df
    
    def determine_model_quality(self, data_count: int, priority: int) -> str:
        """确定模型质量等级"""
        if priority == 0:  # 五大联赛
            if data_count >= 500:
                return "PREMIUM"
            elif data_count >= 200:
                return "STANDARD"
            else:
                return "BACKUP"
        elif priority == 1:  # 一级赛事
            if data_count >= 800:
                return "PREMIUM"
            elif data_count >= 300:
                return "STANDARD"
            elif data_count >= 100:
                return "BACKUP"
            else:
                return "MINIMAL"
        elif priority == 99:  # 杯赛
            if data_count >= 400:
                return "PREMIUM"
            elif data_count >= 150:
                return "STANDARD"
            elif data_count >= 50:
                return "BACKUP"
            else:
                return "MINIMAL"
        else:  # 其他联赛
            if data_count >= 500:
                return "STANDARD"
            elif data_count >= 200:
                return "BACKUP"
            else:
                return "MINIMAL"
    
    def train_league_model(self, league_info: Dict, handicap_line: float) -> Dict:
        """训练单个联赛的单个盘口模型"""
        league_id = league_info['league_id']
        league_name = league_info['league_name']
        priority = league_info['priority']
        
        logger.info(f"训练 {league_name} (ID:{league_id}) 盘口线 {handicap_line} 模型")
        
        # 获取比赛数据
        match_data = self.get_league_match_data(league_id)
        
        if match_data.empty:
            return {'success': False, 'reason': 'no_match_data'}
        
        # 获取赔率数据
        match_ids = match_data['match_id'].tolist()
        odds_data = self.processor.get_odds_data(match_ids, 'OPENING')
        
        if odds_data.empty:
            return {'success': False, 'reason': 'no_odds_data'}
        
        # 创建特征
        features_data = self.processor.create_features(match_data, odds_data)
        
        # 过滤特定盘口线的数据 (只使用goals_line_1)
        if 'goals_line_1' not in features_data.columns:
            return {'success': False, 'reason': 'no_goals_line_1_column'}

        line_data = features_data[features_data['goals_line_1'] == handicap_line].copy()
        
        if line_data.empty:
            return {'success': False, 'reason': 'no_line_data'}
        
        # 确定模型质量
        model_quality = self.determine_model_quality(len(line_data), priority)
        
        # 如果数据太少，仍然训练但标记为备用
        if len(line_data) < 20:
            return {'success': False, 'reason': 'insufficient_data', 'data_count': len(line_data)}
        
        try:
            # 训练模型
            model = GoalsModel("lightgbm", "1.0")
            training_result = model.train(line_data, handicap_line)
            
            # 保存模型
            model_dir = f"saved_models/enhanced_leagues/{model_quality.lower()}"
            os.makedirs(model_dir, exist_ok=True)
            
            # 使用安全的文件名
            safe_league_name = league_name.replace('/', '_').replace('\\', '_').replace(':', '_')
            model_path = f"{model_dir}/goals_lightgbm_v1.0_{safe_league_name}_line_{handicap_line}.joblib"
            model.save_model(model_path)
            
            result = {
                'success': True,
                'league_id': league_id,
                'league_name': league_name,
                'handicap_line': handicap_line,
                'model_quality': model_quality,
                'model_path': model_path,
                'data_count': len(line_data),
                'train_samples': training_result['train_samples'],
                'val_samples': training_result['val_samples'],
                'val_accuracy': training_result['val_accuracy'],
                'cv_mean': training_result['cv_mean'],
                'cv_std': training_result['cv_std']
            }
            
            logger.info(f"✅ {league_name} 盘口{handicap_line} 训练完成 - 质量:{model_quality}, 准确率:{training_result['val_accuracy']:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"❌ {league_name} 盘口{handicap_line} 训练失败: {e}")
            return {'success': False, 'reason': 'training_error', 'error': str(e)}
    
    def train_all_enhanced_models(self, max_leagues_per_tier: Dict[str, int] = None):
        """训练所有增强模型"""
        if max_leagues_per_tier is None:
            max_leagues_per_tier = {
                '五大联赛': 10,
                '一级赛事': 50,
                '杯赛': 20,
                '其他联赛': 30
            }
        
        logger.info("开始增强版联赛模型训练")
        
        # 获取所有有数据的联赛
        all_leagues = self.get_all_leagues_with_data()
        
        results_by_tier = {}
        total_models = 0
        successful_models = 0
        
        # 按层级处理
        for tier_name in ['五大联赛', '一级赛事', '杯赛', '其他联赛']:
            tier_leagues = all_leagues[all_leagues['tier_name'] == tier_name]
            max_leagues = max_leagues_per_tier.get(tier_name, 20)
            
            # 按数据质量排序，取前N个
            tier_leagues = tier_leagues.head(max_leagues)
            
            logger.info(f"\n{'='*80}")
            logger.info(f"训练 {tier_name} ({len(tier_leagues)} 个联赛)")
            logger.info(f"{'='*80}")
            
            tier_results = []
            
            for _, league_info in tier_leagues.iterrows():
                league_id = league_info['league_id']
                league_name = league_info['league_name']
                
                # 获取该联赛的可用盘口线
                available_lines = self.get_available_handicap_lines(league_id)
                
                if not available_lines:
                    logger.warning(f"⚠️ {league_name} 没有可用的盘口线")
                    continue
                
                logger.info(f"🎯 {league_name} - 可用盘口线: {sorted(available_lines)}")
                
                league_models = []
                
                # 为每个盘口线训练模型
                for handicap_line in sorted(available_lines):
                    result = self.train_league_model(league_info.to_dict(), handicap_line)
                    total_models += 1
                    
                    if result['success']:
                        successful_models += 1
                        league_models.append(result)
                
                if league_models:
                    tier_results.append({
                        'league_info': league_info.to_dict(),
                        'models': league_models
                    })
            
            results_by_tier[tier_name] = tier_results
        
        # 保存训练结果
        self.training_results = {
            'by_tier': results_by_tier,
            'summary': {
                'total_models': total_models,
                'successful_models': successful_models,
                'success_rate': successful_models / total_models if total_models > 0 else 0
            }
        }
        
        return self.training_results
    
    def generate_training_summary(self):
        """生成训练总结"""
        if not self.training_results:
            logger.error("没有训练结果可以总结")
            return
        
        logger.info("\n" + "="*100)
        logger.info("增强版联赛模型训练总结")
        logger.info("="*100)
        
        summary = self.training_results['summary']
        logger.info(f"📊 总体统计:")
        logger.info(f"   总训练模型数: {summary['total_models']}")
        logger.info(f"   成功训练模型: {summary['successful_models']}")
        logger.info(f"   成功率: {summary['success_rate']:.1%}")
        
        # 按层级统计
        for tier_name, tier_results in self.training_results['by_tier'].items():
            logger.info(f"\n🎯 {tier_name}:")
            
            tier_leagues = len(tier_results)
            tier_models = sum(len(league['models']) for league in tier_results)
            
            logger.info(f"   联赛数: {tier_leagues}")
            logger.info(f"   模型数: {tier_models}")
            
            # 按质量分组统计
            quality_stats = {}
            for league in tier_results:
                for model in league['models']:
                    quality = model['model_quality']
                    if quality not in quality_stats:
                        quality_stats[quality] = {'count': 0, 'avg_accuracy': []}
                    quality_stats[quality]['count'] += 1
                    quality_stats[quality]['avg_accuracy'].append(model['val_accuracy'])
            
            for quality, stats in quality_stats.items():
                avg_acc = np.mean(stats['avg_accuracy'])
                logger.info(f"     {quality}: {stats['count']} 个模型, 平均准确率: {avg_acc:.3f}")
            
            # 显示最佳模型
            best_models = []
            for league in tier_results:
                for model in league['models']:
                    best_models.append((model['league_name'], model['handicap_line'], 
                                     model['val_accuracy'], model['model_quality']))
            
            if best_models:
                best_models.sort(key=lambda x: x[2], reverse=True)
                logger.info(f"   🏆 最佳模型 (Top 3):")
                for i, (name, line, acc, quality) in enumerate(best_models[:3]):
                    logger.info(f"     {i+1}. {name} 盘口{line}: {acc:.3f} ({quality})")

def main():
    """主函数"""
    logger.info("开始增强版联赛模型训练")
    
    try:
        trainer = EnhancedLeagueTrainer()
        
        # 设置每个层级的最大联赛数
        max_leagues = {
            '五大联赛': 10,    # 训练所有五大联赛
            '一级赛事': 30,    # 训练前30个一级赛事
            '杯赛': 15,        # 训练前15个杯赛
            '其他联赛': 20     # 训练前20个其他联赛
        }
        
        # 训练所有模型
        results = trainer.train_all_enhanced_models(max_leagues)
        
        # 生成总结
        trainer.generate_training_summary()
        
        logger.info("\n🎉 增强版联赛模型训练完成！")
        
    except Exception as e:
        logger.error(f"训练过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
