#!/usr/bin/env python3
"""
足球预测Web API接口
提供预测接口和简单的Web界面
"""

from flask import Flask, request, jsonify, render_template_string
import pandas as pd
import numpy as np
import logging
import pymysql
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import joblib
import os

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("PredictionAPI")

app = Flask(__name__)

class PredictionAPI:
    """预测API类"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.models_cache = {}
        self.load_robust_models()
    
    def load_robust_models(self):
        """加载稳健模型"""
        logger.info("加载稳健预测模型...")
        
        model_dirs = {
            'goals': 'saved_models/robust_models/goals',
            'match_result': 'saved_models/robust_models/match_result',
            'asian_handicap': 'saved_models/robust_models/asian_handicap',
            'corners': 'saved_models/robust_models/corners'
        }
        
        loaded_count = 0
        
        for category, model_dir in model_dirs.items():
            if not os.path.exists(model_dir):
                logger.warning(f"模型目录不存在: {model_dir}")
                continue
                
            self.models_cache[category] = {}
            
            for filename in os.listdir(model_dir):
                if filename.endswith('_robust.joblib'):
                    model_path = os.path.join(model_dir, filename)
                    try:
                        model_info = joblib.load(model_path)
                        model_key = self._parse_model_key(filename)
                        self.models_cache[category][model_key] = model_info
                        loaded_count += 1
                    except Exception as e:
                        logger.error(f"加载模型失败 {filename}: {e}")
        
        logger.info(f"成功加载 {loaded_count} 个稳健模型")
    
    def _parse_model_key(self, filename: str) -> str:
        """解析模型键值"""
        # goals_tier_1_line_2.5_robust.joblib -> tier_1_line_2.5
        parts = filename.replace('_robust.joblib', '').split('_')
        if len(parts) >= 4:
            return '_'.join(parts[1:4])  # tier_X_line_Y
        return filename.replace('_robust.joblib', '')
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def get_matches_to_predict(self, target_date: str = None) -> pd.DataFrame:
        """获取要预测的比赛"""
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')
        
        start_datetime = f"{target_date} 00:00:00"
        end_datetime = (datetime.strptime(target_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d 23:59:59')
        
        with self.get_connection() as conn:
            query = """
            SELECT 
                m.id as match_id,
                m.league_id,
                l.name as league_name,
                l.priority as league_priority,
                m.home_team_name,
                m.away_team_name,
                m.match_datetime,
                m.status,
                
                -- 初盘赔率
                o.goals_line_1,
                o.goals_over_odds_1,
                o.goals_under_odds_1,
                o.asian_handicap_line_1,
                o.asian_home_odds_1,
                o.asian_away_odds_1,
                o.corners_line,
                o.corners_over_odds,
                o.corners_under_odds,
                o.home_win_odds,
                o.draw_odds,
                o.away_win_odds
                
            FROM matches m
            JOIN leagues l ON m.league_id = l.id
            LEFT JOIN odds o ON m.id = o.match_id 
                              AND o.odds_type = 'OPENING' 
                              AND o.bookmaker = '365bet'
            
            WHERE m.match_datetime BETWEEN %s AND %s
            AND m.status <> 'FINISHED'
            AND l.priority IN (0, 1, 99) OR l.priority BETWEEN 2 AND 98
            
            ORDER BY m.match_datetime ASC
            """
            
            df = pd.read_sql(query, conn, params=[start_datetime, end_datetime])
        
        logger.info(f"获取到 {len(df)} 场待预测比赛 ({target_date})")
        return df
    
    def select_best_model(self, category: str, league_priority: int, 
                         handicap_line: float) -> Optional[Dict]:
        """选择最佳模型"""
        if category not in self.models_cache:
            return None
        
        category_models = self.models_cache[category]
        
        # 策略1: 精确匹配
        exact_key = f"tier_{league_priority}_line_{handicap_line}"
        if exact_key in category_models:
            return category_models[exact_key]
        
        # 策略2: 同层级最接近盘口
        tier_models = {k: v for k, v in category_models.items() 
                      if k.startswith(f"tier_{league_priority}_")}
        
        if tier_models and isinstance(handicap_line, (int, float)):
            best_key = min(tier_models.keys(), 
                          key=lambda k: abs(self._extract_line_from_key(k) - handicap_line))
            return tier_models[best_key]
        
        # 策略3: 任意层级相同盘口
        line_models = {k: v for k, v in category_models.items() 
                      if f"line_{handicap_line}" in k}
        
        if line_models:
            best_key = min(line_models.keys(), 
                          key=lambda k: int(k.split('_')[1]) if k.split('_')[1].isdigit() else 999)
            return line_models[best_key]
        
        return None
    
    def _extract_line_from_key(self, model_key: str) -> float:
        """从模型键值提取盘口线"""
        try:
            parts = model_key.split('_')
            for i, part in enumerate(parts):
                if part == 'line' and i + 1 < len(parts):
                    return float(parts[i + 1])
        except:
            pass
        return 0.0
    
    def predict_single_match(self, match_data: Dict) -> Dict:
        """预测单场比赛"""
        match_id = match_data.get('match_id')
        home_team = match_data.get('home_team_name', 'Home')
        away_team = match_data.get('away_team_name', 'Away')
        league_name = match_data.get('league_name', 'Unknown')
        league_priority = match_data.get('league_priority', 999)
        
        predictions = {
            'match_info': {
                'match_id': match_id,
                'home_team': home_team,
                'away_team': away_team,
                'league_name': league_name,
                'match_date': str(match_data.get('match_date', '')),
                'status': match_data.get('status', 'SCHEDULED')
            },
            'predictions': {},
            'summary': {
                'total_predictions': 0,
                'successful_predictions': 0,
                'high_confidence_predictions': 0
            }
        }
        
        # 预测各类别
        categories = [
            ('goals', match_data.get('goals_line_1')),
            ('match_result', '1X2'),
            ('asian_handicap', match_data.get('asian_handicap_line_1')),
            ('corners', match_data.get('corners_line'))
        ]
        
        for category, handicap_line in categories:
            if handicap_line is None or (isinstance(handicap_line, float) and np.isnan(handicap_line)):
                continue
            
            predictions['summary']['total_predictions'] += 1
            
            try:
                # 选择模型
                model_info = self.select_best_model(category, league_priority, handicap_line)
                
                if not model_info:
                    predictions['predictions'][category] = {
                        'success': False,
                        'reason': 'no_suitable_model'
                    }
                    continue
                
                # 创建预测特征 (简化版)
                features = self._create_prediction_features(match_data, category)
                
                if features is None:
                    predictions['predictions'][category] = {
                        'success': False,
                        'reason': 'insufficient_features'
                    }
                    continue
                
                # 进行预测
                model = model_info['model']
                scaler = model_info.get('scaler')
                feature_columns = model_info['feature_columns']
                
                # 准备特征向量
                feature_vector = []
                for col in feature_columns:
                    feature_vector.append(features.get(col, 0))
                
                feature_array = np.array(feature_vector).reshape(1, -1)
                
                # 标准化
                if scaler:
                    feature_array = scaler.transform(feature_array)
                
                # 预测
                prediction = model.predict(feature_array)[0]
                probabilities = model.predict_proba(feature_array)[0]
                
                # 计算置信度
                max_prob = max(probabilities)
                confidence = 'HIGH' if max_prob > 0.7 else ('MEDIUM' if max_prob > 0.6 else 'LOW')
                
                if confidence == 'HIGH':
                    predictions['summary']['high_confidence_predictions'] += 1
                
                # 生成结果描述
                description = self._generate_prediction_description(
                    category, handicap_line, prediction, max_prob
                )
                
                predictions['predictions'][category] = {
                    'success': True,
                    'prediction': int(prediction),
                    'probability': float(max_prob),
                    'confidence': confidence,
                    'description': description,
                    'handicap_line': handicap_line,
                    'model_tier': model_info.get('tier', 'unknown')
                }
                
                predictions['summary']['successful_predictions'] += 1
                
            except Exception as e:
                logger.error(f"预测 {category} 失败: {e}")
                predictions['predictions'][category] = {
                    'success': False,
                    'reason': 'prediction_error',
                    'error': str(e)
                }
        
        return predictions
    
    def _create_prediction_features(self, match_data: Dict, category: str) -> Optional[Dict]:
        """创建预测特征 (简化版)"""
        features = {}
        
        # 基础赔率特征
        home_odds = match_data.get('home_win_odds')
        draw_odds = match_data.get('draw_odds')
        away_odds = match_data.get('away_win_odds')
        
        if home_odds and draw_odds and away_odds:
            home_prob = 1 / home_odds
            draw_prob = 1 / draw_odds
            away_prob = 1 / away_odds
            total_prob = home_prob + draw_prob + away_prob
            
            features['home_win_prob_norm'] = home_prob / total_prob
            features['draw_prob_norm'] = draw_prob / total_prob
            features['away_win_prob_norm'] = away_prob / total_prob
        else:
            features['home_win_prob_norm'] = 0.33
            features['draw_prob_norm'] = 0.33
            features['away_win_prob_norm'] = 0.33
        
        # 默认特征值 (实际应用中应该从历史数据计算)
        features['shots_diff'] = 0
        features['attacks_diff'] = 0
        features['possession_diff'] = 0
        
        # 时间特征
        match_date = match_data.get('match_date')
        if match_date:
            if isinstance(match_date, str):
                match_date = datetime.strptime(match_date[:10], '%Y-%m-%d')
            features['month'] = match_date.month
            features['day_of_week'] = match_date.weekday()
            features['is_weekend'] = 1 if match_date.weekday() >= 5 else 0
        else:
            features['month'] = datetime.now().month
            features['day_of_week'] = datetime.now().weekday()
            features['is_weekend'] = 1 if datetime.now().weekday() >= 5 else 0
        
        # 联赛特征
        league_priority = match_data.get('league_priority', 999)
        features['is_top_league'] = 1 if league_priority == 0 else 0
        features['is_tier1_league'] = 1 if league_priority == 1 else 0
        
        return features
    
    def _generate_prediction_description(self, category: str, handicap_line: float, 
                                       prediction: int, probability: float) -> str:
        """生成预测描述"""
        if category == 'goals':
            return f"{'大球' if prediction == 1 else '小球'} {handicap_line} (概率: {probability:.1%})"
        elif category == 'match_result':
            results = ['主胜', '平局', '客胜']
            return f"{results[prediction]} (概率: {probability:.1%})"
        elif category == 'asian_handicap':
            return f"{'主队赢盘' if prediction == 1 else '主队输盘'} {handicap_line} (概率: {probability:.1%})"
        elif category == 'corners':
            return f"{'大角球' if prediction == 1 else '小角球'} {handicap_line} (概率: {probability:.1%})"
        else:
            return f"预测结果: {prediction} (概率: {probability:.1%})"

# 创建API实例
prediction_api = PredictionAPI()

# Web界面HTML模板
WEB_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>足球预测系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; margin-bottom: 30px; }
        .controls { margin-bottom: 20px; padding: 15px; background: #ecf0f1; border-radius: 5px; }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .match-card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: #fff; }
        .match-header { font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
        .prediction { margin: 5px 0; padding: 8px; border-radius: 3px; }
        .high-confidence { background: #d5f4e6; border-left: 4px solid #27ae60; }
        .medium-confidence { background: #fff3cd; border-left: 4px solid #ffc107; }
        .low-confidence { background: #f8d7da; border-left: 4px solid #dc3545; }
        .loading { text-align: center; color: #7f8c8d; }
        .error { color: #e74c3c; background: #fadbd8; padding: 10px; border-radius: 5px; }
        .success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚽ 足球预测系统</h1>
            <p>基于机器学习的多类别足球比赛预测</p>
        </div>
        
        <div class="controls">
            <label for="predict-date">选择预测日期:</label>
            <input type="date" id="predict-date" value="{{ today }}">
            <button class="btn btn-success" onclick="predictMatches()">🎯 开始预测</button>
            <button class="btn" onclick="loadMatches()">📋 加载比赛</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function loadMatches() {
            const date = document.getElementById('predict-date').value;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="loading">正在加载比赛...</div>';
            
            fetch(`/api/matches?date=${date}`)
                .then(response => response.json())
                .then(data => {
                    if (data.matches && data.matches.length > 0) {
                        let html = `<div class="success">找到 ${data.matches.length} 场比赛</div>`;
                        data.matches.forEach(match => {
                            html += `
                                <div class="match-card">
                                    <div class="match-header">
                                        ${match.home_team_name} vs ${match.away_team_name}
                                    </div>
                                    <div>联赛: ${match.league_name} | 时间: ${match.match_date}</div>
                                    <div>状态: ${match.status}</div>
                                </div>
                            `;
                        });
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = '<div class="error">没有找到比赛</div>';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<div class="error">加载失败: ' + error.message + '</div>';
                });
        }
        
        function predictMatches() {
            const date = document.getElementById('predict-date').value;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="loading">正在进行预测...</div>';
            
            fetch(`/api/predict?date=${date}`)
                .then(response => response.json())
                .then(data => {
                    if (data.predictions && data.predictions.length > 0) {
                        let html = `<div class="success">完成 ${data.predictions.length} 场比赛预测</div>`;
                        
                        data.predictions.forEach(pred => {
                            const match = pred.match_info;
                            const summary = pred.summary;
                            
                            html += `
                                <div class="match-card">
                                    <div class="match-header">
                                        ${match.home_team} vs ${match.away_team}
                                    </div>
                                    <div>联赛: ${match.league_name} | 时间: ${match.match_date}</div>
                                    <div>预测成功: ${summary.successful_predictions}/${summary.total_predictions} | 高置信度: ${summary.high_confidence_predictions}</div>
                                    <div style="margin-top: 10px;">
                            `;
                            
                            Object.entries(pred.predictions).forEach(([category, result]) => {
                                if (result.success) {
                                    const confidenceClass = result.confidence.toLowerCase() + '-confidence';
                                    const icons = {
                                        'goals': '⚽',
                                        'match_result': '🏆',
                                        'asian_handicap': '⚖️',
                                        'corners': '🚩'
                                    };
                                    
                                    html += `
                                        <div class="prediction ${confidenceClass}">
                                            ${icons[category] || '📊'} ${result.description}
                                            <span style="float: right; font-size: 0.9em;">置信度: ${result.confidence}</span>
                                        </div>
                                    `;
                                }
                            });
                            
                            html += '</div></div>';
                        });
                        
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = '<div class="error">没有预测结果</div>';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<div class="error">预测失败: ' + error.message + '</div>';
                });
        }
        
        // 页面加载时自动加载今天的比赛
        window.onload = function() {
            loadMatches();
        };
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    today = datetime.now().strftime('%Y-%m-%d')
    return render_template_string(WEB_TEMPLATE, today=today)

@app.route('/api/matches')
def get_matches():
    """获取比赛列表API"""
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    
    try:
        matches_df = prediction_api.get_matches_to_predict(date)
        matches = matches_df.to_dict('records')
        
        return jsonify({
            'success': True,
            'date': date,
            'count': len(matches),
            'matches': matches
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/predict')
def predict_matches():
    """预测比赛API"""
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    
    try:
        matches_df = prediction_api.get_matches_to_predict(date)
        
        if matches_df.empty:
            return jsonify({
                'success': True,
                'date': date,
                'count': 0,
                'predictions': []
            })
        
        predictions = []
        
        for _, match_row in matches_df.iterrows():
            match_data = match_row.to_dict()
            prediction_result = prediction_api.predict_single_match(match_data)
            predictions.append(prediction_result)
        
        return jsonify({
            'success': True,
            'date': date,
            'count': len(predictions),
            'predictions': predictions
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("启动足球预测Web API...")
    app.run(host='0.0.0.0', port=5000, debug=True)
