#!/usr/bin/env python3
"""
数据质量修复和模型重训练系统
使用有效的历史数据重新训练所有模型
"""

import pandas as pd
import numpy as np
import logging
import os
import pymysql
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_quality_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("DataQualityFix")

class DataQualityFixAndRetrain:
    """数据质量修复和重训练系统"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.cutoff_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
        self.prediction_categories = self._load_prediction_categories()
        
    def _load_prediction_categories(self) -> Dict:
        """加载预测类别配置"""
        return {
            'goals': {
                'name': '进球大小球',
                'target_field': 'total_goals',
                'line_field': 'goals_line_1',
                'odds_fields': ['goals_over_odds_1', 'goals_under_odds_1'],
                'common_lines': [2.5, 3.5, 2.25, 2.75, 3.0, 2.0],
                'min_data_per_line': 100,
                'icon': '⚽'
            },
            'match_result': {
                'name': '胜平负',
                'target_field': 'result',
                'line_field': None,
                'odds_fields': ['home_win_odds', 'draw_odds', 'away_win_odds'],
                'common_lines': ['1X2'],
                'min_data_per_line': 100,
                'icon': '🏆'
            },
            'asian_handicap': {
                'name': '让球(亚盘)',
                'target_field': 'asian_result',
                'line_field': 'asian_handicap_line_1',
                'odds_fields': ['asian_home_odds_1', 'asian_away_odds_1'],
                'common_lines': [0.0, -0.25, -0.5, 0.25, -0.75, -1.0],
                'min_data_per_line': 80,
                'icon': '⚖️'
            },
            'corners': {
                'name': '角球大小球',
                'target_field': 'total_corners',
                'line_field': 'corners_line',
                'odds_fields': ['corners_over_odds', 'corners_under_odds'],
                'common_lines': [9.5, 8.5, 10.5, 7.5, 11.5],
                'min_data_per_line': 60,
                'icon': '🚩'
            },
            'league_tiers': {
                'tier_0': {'name': '五大联赛', 'priority': 0},
                'tier_1': {'name': '一级赛事', 'priority': 1},
                'tier_99': {'name': '杯赛', 'priority': 99},
                'tier_other': {'name': '其他赛事', 'priority_range': [2, 98]}
            }
        }
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def analyze_data_quality(self) -> Dict:
        """分析数据质量"""
        logger.info("分析数据质量...")
        logger.info(f"使用截止时间: {self.cutoff_date}")
        
        quality_report = {}
        
        with self.get_connection() as conn:
            # 检查有效数据量
            data_quality_query = """
            SELECT 
                COUNT(*) as total_matches,
                COUNT(CASE WHEN ms.total_goals IS NOT NULL THEN 1 END) as matches_with_goals,
                COUNT(CASE WHEN ms.total_corners IS NOT NULL THEN 1 END) as matches_with_corners,
                COUNT(CASE WHEN o.home_win_odds IS NOT NULL THEN 1 END) as matches_with_1x2_odds,
                COUNT(CASE WHEN o.goals_line_1 IS NOT NULL THEN 1 END) as matches_with_goals_line,
                COUNT(CASE WHEN o.asian_handicap_line_1 IS NOT NULL THEN 1 END) as matches_with_asian_line,
                COUNT(CASE WHEN o.corners_line IS NOT NULL THEN 1 END) as matches_with_corners_line,
                MIN(m.match_datetime) as earliest_date,
                MAX(m.match_datetime) as latest_date
            FROM matches m
            JOIN match_stats ms ON m.id = ms.match_id
            LEFT JOIN odds o ON m.id = o.match_id 
                              AND o.bookmaker = '365bet' 
                              AND o.odds_type = 'OPENING'
            WHERE m.match_datetime < %s
            AND m.match_datetime >= DATE_SUB(%s, INTERVAL 3 YEAR)
            AND m.issync = 0
            AND m.issyncodd = 0
            AND m.status = 'FINISHED'
            """
            
            df = pd.read_sql(data_quality_query, conn, params=[self.cutoff_date, self.cutoff_date])
            
            if not df.empty:
                row = df.iloc[0]
                quality_report['overview'] = {
                    'total_matches': row['total_matches'],
                    'matches_with_goals': row['matches_with_goals'],
                    'matches_with_corners': row['matches_with_corners'],
                    'matches_with_1x2_odds': row['matches_with_1x2_odds'],
                    'matches_with_goals_line': row['matches_with_goals_line'],
                    'matches_with_asian_line': row['matches_with_asian_line'],
                    'matches_with_corners_line': row['matches_with_corners_line'],
                    'date_range': f"{row['earliest_date']} 到 {row['latest_date']}"
                }
                
                # 计算完整性百分比
                total = row['total_matches']
                if total > 0:
                    quality_report['completeness'] = {
                        'goals_data': row['matches_with_goals'] / total * 100,
                        'corners_data': row['matches_with_corners'] / total * 100,
                        '1x2_odds': row['matches_with_1x2_odds'] / total * 100,
                        'goals_line': row['matches_with_goals_line'] / total * 100,
                        'asian_line': row['matches_with_asian_line'] / total * 100,
                        'corners_line': row['matches_with_corners_line'] / total * 100
                    }
        
        self._display_quality_report(quality_report)
        return quality_report
    
    def _display_quality_report(self, report: Dict):
        """显示数据质量报告"""
        logger.info("\n" + "="*80)
        logger.info("📊 数据质量分析报告")
        logger.info("="*80)
        
        if 'overview' in report:
            overview = report['overview']
            logger.info(f"📅 数据时间范围: {overview['date_range']}")
            logger.info(f"📊 总比赛数: {overview['total_matches']:,}")
            logger.info(f"⚽ 有进球数据: {overview['matches_with_goals']:,}")
            logger.info(f"🚩 有角球数据: {overview['matches_with_corners']:,}")
            logger.info(f"🏆 有胜平负赔率: {overview['matches_with_1x2_odds']:,}")
            logger.info(f"📈 有进球盘口: {overview['matches_with_goals_line']:,}")
            logger.info(f"⚖️ 有亚盘数据: {overview['matches_with_asian_line']:,}")
            logger.info(f"🎯 有角球盘口: {overview['matches_with_corners_line']:,}")
        
        if 'completeness' in report:
            completeness = report['completeness']
            logger.info(f"\n📈 数据完整性:")
            logger.info(f"  进球数据: {completeness['goals_data']:.1f}%")
            logger.info(f"  角球数据: {completeness['corners_data']:.1f}%")
            logger.info(f"  胜平负赔率: {completeness['1x2_odds']:.1f}%")
            logger.info(f"  进球盘口: {completeness['goals_line']:.1f}%")
            logger.info(f"  亚盘数据: {completeness['asian_line']:.1f}%")
            logger.info(f"  角球盘口: {completeness['corners_line']:.1f}%")
    
    def get_clean_training_data(self, category: str) -> pd.DataFrame:
        """获取清洁的训练数据"""
        logger.info(f"获取 {category} 的清洁训练数据...")
        
        category_config = self.prediction_categories[category]
        
        with self.get_connection() as conn:
            if category == 'goals':
                query = """
                SELECT 
                    m.id as match_id, m.league_id, l.name as league_name, l.priority as league_priority,
                    m.home_team_name, m.away_team_name, m.match_date,
                    ms.home_goals, ms.away_goals, ms.total_goals,
                    ms.home_corners, ms.away_corners, ms.total_corners,
                    ms.home_shots, ms.away_shots, ms.home_shots_on_target, ms.away_shots_on_target,
                    ms.home_attacks, ms.away_attacks, ms.home_dangerous_attacks, ms.away_dangerous_attacks,
                    ms.home_possession, ms.away_possession,
                    o1.goals_line_1, o1.goals_over_odds_1, o1.goals_under_odds_1,
                    o1.home_win_odds, o1.draw_odds, o1.away_win_odds,
                    o2.goals_line_1 as live_goals_line, o2.goals_over_odds_1 as live_goals_over_odds
                FROM matches m
                JOIN leagues l ON m.league_id = l.id
                JOIN match_stats ms ON m.id = ms.match_id
                JOIN odds o1 ON m.id = o1.match_id AND o1.odds_type = 'OPENING' AND o1.bookmaker = '365bet'
                LEFT JOIN odds o2 ON m.id = o2.match_id AND o2.odds_type = 'LIVE' AND o2.bookmaker = '365bet'
                WHERE m.match_datetime < %s
                AND m.match_datetime >= DATE_SUB(%s, INTERVAL 3 YEAR)
                AND m.issync = 0 AND m.issyncodd = 0
                AND m.status = 'FINISHED'
                AND ms.total_goals IS NOT NULL
                AND o1.goals_line_1 IS NOT NULL
                AND o1.goals_line_1 BETWEEN 0.5 AND 6.5
                ORDER BY m.match_datetime DESC
                """
            
            elif category == 'match_result':
                query = """
                SELECT 
                    m.id as match_id, m.league_id, l.name as league_name, l.priority as league_priority,
                    m.home_team_name, m.away_team_name, m.match_date,
                    ms.home_goals, ms.away_goals, ms.total_goals,
                    ms.home_corners, ms.away_corners, ms.total_corners,
                    ms.home_shots, ms.away_shots, ms.home_shots_on_target, ms.away_shots_on_target,
                    ms.home_attacks, ms.away_attacks, ms.home_dangerous_attacks, ms.away_dangerous_attacks,
                    ms.home_possession, ms.away_possession,
                    CASE WHEN ms.home_goals > ms.away_goals THEN 'H'
                         WHEN ms.home_goals = ms.away_goals THEN 'D'
                         ELSE 'A' END as result,
                    o1.home_win_odds, o1.draw_odds, o1.away_win_odds,
                    o2.home_win_odds as live_home_odds, o2.draw_odds as live_draw_odds
                FROM matches m
                JOIN leagues l ON m.league_id = l.id
                JOIN match_stats ms ON m.id = ms.match_id
                JOIN odds o1 ON m.id = o1.match_id AND o1.odds_type = 'OPENING' AND o1.bookmaker = '365bet'
                LEFT JOIN odds o2 ON m.id = o2.match_id AND o2.odds_type = 'LIVE' AND o2.bookmaker = '365bet'
                WHERE m.match_datetime < %s
                AND m.match_datetime >= DATE_SUB(%s, INTERVAL 3 YEAR)
                AND m.issync = 0 AND m.issyncodd = 0
                AND m.status = 'FINISHED'
                AND ms.home_goals IS NOT NULL AND ms.away_goals IS NOT NULL
                AND o1.home_win_odds BETWEEN 1.1 AND 50.0
                AND o1.draw_odds BETWEEN 1.1 AND 50.0
                AND o1.away_win_odds BETWEEN 1.1 AND 50.0
                ORDER BY m.match_datetime DESC
                """
            
            elif category == 'asian_handicap':
                query = """
                SELECT 
                    m.id as match_id, m.league_id, l.name as league_name, l.priority as league_priority,
                    m.home_team_name, m.away_team_name, m.match_date,
                    ms.home_goals, ms.away_goals, ms.total_goals,
                    ms.home_corners, ms.away_corners, ms.total_corners,
                    ms.home_shots, ms.away_shots, ms.home_shots_on_target, ms.away_shots_on_target,
                    ms.home_attacks, ms.away_attacks, ms.home_dangerous_attacks, ms.away_dangerous_attacks,
                    ms.home_possession, ms.away_possession,
                    o1.asian_handicap_line_1, o1.asian_home_odds_1, o1.asian_away_odds_1,
                    o1.home_win_odds, o1.draw_odds, o1.away_win_odds,
                    o2.asian_handicap_line_1 as live_asian_line, o2.asian_home_odds_1 as live_asian_home_odds
                FROM matches m
                JOIN leagues l ON m.league_id = l.id
                JOIN match_stats ms ON m.id = ms.match_id
                JOIN odds o1 ON m.id = o1.match_id AND o1.odds_type = 'OPENING' AND o1.bookmaker = '365bet'
                LEFT JOIN odds o2 ON m.id = o2.match_id AND o2.odds_type = 'LIVE' AND o2.bookmaker = '365bet'
                WHERE m.match_datetime < %s
                AND m.match_datetime >= DATE_SUB(%s, INTERVAL 3 YEAR)
                AND m.issync = 0 AND m.issyncodd = 0
                AND m.status = 'FINISHED'
                AND ms.home_goals IS NOT NULL AND ms.away_goals IS NOT NULL
                AND o1.asian_handicap_line_1 IS NOT NULL
                AND o1.asian_handicap_line_1 BETWEEN -3.0 AND 3.0
                ORDER BY m.match_datetime DESC
                """
            
            elif category == 'corners':
                query = """
                SELECT 
                    m.id as match_id, m.league_id, l.name as league_name, l.priority as league_priority,
                    m.home_team_name, m.away_team_name, m.match_date,
                    ms.home_goals, ms.away_goals, ms.total_goals,
                    ms.home_corners, ms.away_corners, ms.total_corners,
                    ms.home_shots, ms.away_shots, ms.home_shots_on_target, ms.away_shots_on_target,
                    ms.home_attacks, ms.away_attacks, ms.home_dangerous_attacks, ms.away_dangerous_attacks,
                    ms.home_possession, ms.away_possession,
                    o1.corners_line, o1.corners_over_odds, o1.corners_under_odds,
                    o1.home_win_odds, o1.draw_odds, o1.away_win_odds,
                    o2.corners_line as live_corners_line, o2.corners_over_odds as live_corners_over_odds
                FROM matches m
                JOIN leagues l ON m.league_id = l.id
                JOIN match_stats ms ON m.id = ms.match_id
                JOIN odds o1 ON m.id = o1.match_id AND o1.odds_type = 'OPENING' AND o1.bookmaker = '365bet'
                LEFT JOIN odds o2 ON m.id = o2.match_id AND o2.odds_type = 'LIVE' AND o2.bookmaker = '365bet'
                WHERE m.match_datetime < %s
                AND m.match_datetime >= DATE_SUB(%s, INTERVAL 3 YEAR)
                AND m.issync = 0 AND m.issyncodd = 0
                AND m.status = 'FINISHED'
                AND ms.total_corners IS NOT NULL
                AND o1.corners_line IS NOT NULL
                AND o1.corners_line BETWEEN 3.5 AND 20.5
                ORDER BY m.match_datetime DESC
                """
            
            df = pd.read_sql(query, conn, params=[self.cutoff_date, self.cutoff_date])
        
        logger.info(f"获取到 {category} 清洁数据: {len(df)} 场比赛")
        return df
    
    def create_enhanced_features(self, data: pd.DataFrame, category: str) -> pd.DataFrame:
        """创建增强特征 - 防止过拟合"""
        df = data.copy()
        
        # 1. 基础统计特征
        if 'home_shots' in df.columns and 'away_shots' in df.columns:
            df['shots_diff'] = df['home_shots'] - df['away_shots']
            df['shots_total'] = df['home_shots'] + df['away_shots']
            df['shots_ratio'] = df['home_shots'] / (df['away_shots'] + 1)
        
        if 'home_attacks' in df.columns and 'away_attacks' in df.columns:
            df['attacks_diff'] = df['home_attacks'] - df['away_attacks']
            df['attacks_total'] = df['home_attacks'] + df['away_attacks']
        
        if 'home_possession' in df.columns and 'away_possession' in df.columns:
            df['possession_diff'] = df['home_possession'] - df['away_possession']
        
        # 2. 赔率概率特征 (标准化)
        if 'home_win_odds' in df.columns:
            df['home_win_prob'] = 1 / df['home_win_odds'].fillna(3)
            df['draw_prob'] = 1 / df['draw_odds'].fillna(3)
            df['away_win_prob'] = 1 / df['away_win_odds'].fillna(3)
            
            total_prob = df['home_win_prob'] + df['draw_prob'] + df['away_win_prob']
            df['home_win_prob_norm'] = df['home_win_prob'] / total_prob
            df['draw_prob_norm'] = df['draw_prob'] / total_prob
            df['away_win_prob_norm'] = df['away_win_prob'] / total_prob
            
            # 赔率差异特征
            df['odds_variance'] = np.var([df['home_win_odds'], df['draw_odds'], df['away_win_odds']], axis=0)
        
        # 3. 时间特征
        df['match_date'] = pd.to_datetime(df['match_date'])
        df['month'] = df['match_date'].dt.month
        df['day_of_week'] = df['match_date'].dt.dayofweek
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        
        # 4. 联赛特征
        df['is_top_league'] = (df['league_priority'] == 0).astype(int)
        df['is_tier1_league'] = (df['league_priority'] == 1).astype(int)
        df['is_cup'] = (df['league_priority'] == 99).astype(int)
        
        # 5. 赔率变化特征 (如果有即时盘数据)
        if category == 'goals' and 'live_goals_line' in df.columns:
            df['goals_line_change'] = df['live_goals_line'] - df['goals_line_1']
            df['goals_line_change'] = df['goals_line_change'].fillna(0)
        
        # 填充缺失值
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].fillna(0)
        
        # 移除异常值 (使用IQR方法)
        for col in ['shots_diff', 'attacks_diff', 'possession_diff']:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df[col] = df[col].clip(lower_bound, upper_bound)
        
        return df
    
    def train_robust_model(self, data: pd.DataFrame, category: str, 
                          tier: int, line_value: float) -> Dict:
        """训练防过拟合的稳健模型"""
        
        category_config = self.prediction_categories[category]
        min_data = category_config['min_data_per_line']
        
        # 过滤数据
        if category == 'match_result':
            filtered_data = data.copy()
        else:
            line_field = category_config['line_field']
            if line_field in data.columns:
                filtered_data = data[abs(data[line_field] - line_value) < 0.1].copy()
            else:
                filtered_data = data.copy()
        
        if len(filtered_data) < min_data:
            return {
                'success': False, 
                'reason': 'insufficient_data', 
                'data_count': len(filtered_data),
                'min_required': min_data
            }
        
        # 创建目标变量
        if category == 'goals':
            y = (filtered_data['total_goals'] > line_value).astype(int)
        elif category == 'match_result':
            y = filtered_data['result'].map({'H': 0, 'D': 1, 'A': 2})
        elif category == 'asian_handicap':
            home_adjusted = filtered_data['home_goals'] + line_value
            y = (home_adjusted > filtered_data['away_goals']).astype(int)
        elif category == 'corners':
            y = (filtered_data['total_corners'] > line_value).astype(int)
        
        # 选择特征 (减少特征数量防止过拟合)
        feature_columns = [
            'home_win_prob_norm', 'draw_prob_norm', 'away_win_prob_norm',
            'shots_diff', 'attacks_diff', 'possession_diff',
            'month', 'day_of_week', 'is_weekend',
            'is_top_league', 'is_tier1_league'
        ]
        
        # 只保留存在的特征
        available_features = [f for f in feature_columns if f in filtered_data.columns]
        X = filtered_data[available_features].fillna(0)
        
        # 特征标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        X_scaled = pd.DataFrame(X_scaled, columns=available_features, index=X.index)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.25, random_state=42, stratify=y
        )
        
        # 训练防过拟合模型
        if category_config.get('prediction_type') == 'multiclass':
            model = RandomForestClassifier(
                n_estimators=100,      # 适中的树数量
                max_depth=6,           # 限制深度防止过拟合
                min_samples_split=20,  # 增加最小分割样本数
                min_samples_leaf=10,   # 增加叶子节点最小样本数
                max_features='sqrt',   # 限制特征数量
                random_state=42,
                class_weight='balanced'  # 处理类别不平衡
            )
        else:
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=6,
                min_samples_split=20,
                min_samples_leaf=10,
                max_features='sqrt',
                random_state=42,
                class_weight='balanced'
            )
        
        model.fit(X_train, y_train)
        
        # 评估
        train_score = model.score(X_train, y_train)
        test_score = model.score(X_test, y_test)
        
        # 交叉验证
        cv_scores = cross_val_score(model, X_scaled, y, cv=5, scoring='accuracy')
        cv_mean = cv_scores.mean()
        cv_std = cv_scores.std()
        
        # 检查过拟合
        overfitting_score = train_score - test_score
        is_overfitting = overfitting_score > 0.1  # 训练准确率比测试准确率高10%以上
        
        # 保存模型
        model_dir = f"saved_models/robust_models/{category}"
        os.makedirs(model_dir, exist_ok=True)
        
        tier_name = {0: '五大联赛', 1: '一级赛事', 99: '杯赛'}.get(tier, '其他联赛')
        model_filename = f"{category}_tier_{tier}_line_{line_value}_robust.joblib"
        model_path = os.path.join(model_dir, model_filename)
        
        model_info = {
            'model': model,
            'scaler': scaler,
            'feature_columns': available_features,
            'category': category,
            'tier': tier,
            'line_value': line_value,
            'training_date': datetime.now().isoformat(),
            'data_count': len(filtered_data)
        }
        
        joblib.dump(model_info, model_path)
        
        result = {
            'success': True,
            'path': model_path,
            'data_count': len(filtered_data),
            'train_score': train_score,
            'test_score': test_score,
            'cv_mean': cv_mean,
            'cv_std': cv_std,
            'overfitting_score': overfitting_score,
            'is_overfitting': is_overfitting,
            'feature_count': len(available_features)
        }
        
        # 记录详细评估
        if len(np.unique(y_test)) > 1:
            y_pred = model.predict(X_test)
            result['classification_report'] = classification_report(y_test, y_pred, output_dict=True)
        
        return result

def main():
    """主函数"""
    logger.info("启动数据质量修复和模型重训练系统")
    
    try:
        system = DataQualityFixAndRetrain()
        
        # 1. 分析数据质量
        quality_report = system.analyze_data_quality()
        
        if quality_report.get('overview', {}).get('total_matches', 0) < 1000:
            logger.error("有效数据量不足，无法进行模型训练")
            return
        
        # 2. 重训练所有类别的模型
        categories = ['goals', 'match_result', 'asian_handicap', 'corners']
        
        all_results = {}
        total_models = 0
        successful_models = 0
        
        for category in categories:
            logger.info(f"\n{'='*80}")
            logger.info(f"重训练 {system.prediction_categories[category]['icon']} {system.prediction_categories[category]['name']} 模型")
            logger.info(f"{'='*80}")
            
            # 获取清洁数据
            clean_data = system.get_clean_training_data(category)
            
            if clean_data.empty:
                logger.warning(f"{category} 没有清洁数据")
                continue
            
            # 创建特征
            enhanced_data = system.create_enhanced_features(clean_data, category)
            
            # 按层级训练模型
            category_results = {}
            
            for tier in [0, 1, 99]:  # 五大联赛、一级赛事、杯赛
                tier_data = enhanced_data[enhanced_data['league_priority'] == tier]
                
                if tier_data.empty:
                    continue
                
                tier_name = {0: '五大联赛', 1: '一级赛事', 99: '杯赛'}[tier]
                logger.info(f"  训练 {tier_name} 模型...")
                
                # 获取该层级的盘口线
                config = system.prediction_categories[category]
                lines_to_train = config['common_lines'][:3]  # 只训练前3个最重要的盘口线
                
                for line_value in lines_to_train:
                    total_models += 1
                    
                    try:
                        result = system.train_robust_model(tier_data, category, tier, line_value)
                        
                        if result['success']:
                            successful_models += 1
                            model_key = f"tier_{tier}_line_{line_value}"
                            category_results[model_key] = result
                            
                            # 显示结果
                            overfitting_status = "⚠️过拟合" if result['is_overfitting'] else "✅正常"
                            logger.info(f"    ✅ {tier_name} 盘口{line_value}: "
                                      f"测试准确率{result['test_score']:.3f} "
                                      f"(CV:{result['cv_mean']:.3f}±{result['cv_std']:.3f}) "
                                      f"{overfitting_status} "
                                      f"({result['data_count']}场数据)")
                        else:
                            logger.warning(f"    ❌ {tier_name} 盘口{line_value}: "
                                         f"{result['reason']} (需要{result.get('min_required', 0)}场，"
                                         f"实际{result['data_count']}场)")
                    
                    except Exception as e:
                        logger.error(f"    💥 {tier_name} 盘口{line_value} 训练异常: {e}")
            
            all_results[category] = category_results
        
        # 3. 显示总结
        logger.info(f"\n{'='*100}")
        logger.info("🎉 数据质量修复和模型重训练完成")
        logger.info(f"{'='*100}")
        logger.info(f"📊 训练统计:")
        logger.info(f"  总模型数: {total_models}")
        logger.info(f"  成功训练: {successful_models}")
        logger.info(f"  成功率: {successful_models/total_models*100:.1f}%")
        logger.info(f"📁 模型保存位置: saved_models/robust_models/")
        logger.info(f"🔧 使用条件: match_date < 当前时间-7天 AND issync=0 AND issyncodd=0")
        logger.info(f"⚡ 防过拟合: 限制模型复杂度、特征标准化、交叉验证")
        
    except Exception as e:
        logger.error(f"系统运行失败: {e}")
        raise

if __name__ == "__main__":
    main()
