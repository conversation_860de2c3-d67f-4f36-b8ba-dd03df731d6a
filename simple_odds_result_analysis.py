#!/usr/bin/env python3
"""
简化版赔率变化与结果关联分析
专门验证OPENING -> LIVE变化对最终结果的预测价值
"""

import pandas as pd
import numpy as np
import logging
import pymysql
from datetime import datetime, timedelta

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("OddsResultAnalysis")

def get_odds_and_results():
    """获取赔率变化和比赛结果数据"""
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    with pymysql.connect(**DB_CONFIG) as conn:
        # 先检查有哪些博彩公司
        check_query = "SELECT DISTINCT bookmaker FROM odds LIMIT 10"
        bookmakers_df = pd.read_sql(check_query, conn)
        logger.info(f"可用博彩公司: {list(bookmakers_df['bookmaker'])}")

        # 获取有完整赔率数据的比赛 (使用365bet，必须同时有初盘和即时盘)
        query = """
        SELECT
            m.id as match_id,
            m.home_team_name,
            m.away_team_name,
            l.name as league_name,
            l.priority,
            ms.total_goals,

            -- 初盘数据
            o1.goals_line_1 as opening_line,
            o1.goals_over_odds_1 as opening_over_odds,
            o1.goals_under_odds_1 as opening_under_odds,

            -- 即时盘数据
            o2.goals_line_1 as live_line,
            o2.goals_over_odds_1 as live_over_odds,
            o2.goals_under_odds_1 as live_under_odds

        FROM matches m
        JOIN leagues l ON m.league_id = l.id
        JOIN match_stats ms ON m.id = ms.match_id
        JOIN odds o1 ON m.id = o1.match_id
                     AND o1.odds_type = 'OPENING'
                     AND o1.bookmaker = '365bet'
        JOIN odds o2 ON m.id = o2.match_id
                     AND o2.odds_type = 'LIVE'
                     AND o2.bookmaker = '365bet'

        WHERE l.priority IN (0, 1)  -- 五大联赛和一级赛事
        AND m.match_date >= %s
        AND m.match_date <= %s
        AND ms.total_goals IS NOT NULL
        -- 确保初盘和即时盘都有完整的进球盘口数据
        AND o1.goals_line_1 IS NOT NULL
        AND o1.goals_over_odds_1 IS NOT NULL
        AND o1.goals_under_odds_1 IS NOT NULL
        AND o2.goals_line_1 IS NOT NULL
        AND o2.goals_over_odds_1 IS NOT NULL
        AND o2.goals_under_odds_1 IS NOT NULL

        ORDER BY m.match_date DESC
        LIMIT 300
        """
        
        df = pd.read_sql(query, conn, params=[start_date, end_date])
    
    logger.info(f"获取到 {len(df)} 场有完整赔率数据的比赛")
    return df

def analyze_odds_changes_vs_results(df):
    """分析赔率变化与结果的关联"""
    
    if df.empty:
        logger.error("没有数据可分析")
        return
    
    # 计算变化特征
    df['line_change'] = df['live_line'] - df['opening_line']
    df['over_odds_change'] = df['live_over_odds'] - df['opening_over_odds']
    df['under_odds_change'] = df['live_under_odds'] - df['opening_under_odds']
    
    # 分类变化方向
    df['line_direction'] = df['line_change'].apply(lambda x: 
        '升盘' if x > 0.1 else ('降盘' if x < -0.1 else '平盘'))
    
    # 市场情绪 (基于赔率变化)
    df['market_sentiment'] = df.apply(lambda row:
        'FAVOR_OVER' if row['over_odds_change'] < -0.1 and row['under_odds_change'] > 0.1
        else 'FAVOR_UNDER' if row['over_odds_change'] > 0.1 and row['under_odds_change'] < -0.1
        else 'NEUTRAL', axis=1)
    
    logger.info("="*80)
    logger.info("赔率变化与最终结果关联分析")
    logger.info("="*80)
    
    # 1. 基础统计
    logger.info(f"\n📊 基础统计 ({len(df)} 场比赛):")
    logger.info(f"   平均进球数: {df['total_goals'].mean():.2f}")
    logger.info(f"   大2.5球比例: {(df['total_goals'] > 2.5).mean():.1%}")
    logger.info(f"   平均盘口变化: {df['line_change'].mean():.3f}")
    
    # 2. 盘口变化方向分析
    logger.info(f"\n🎯 盘口变化方向与结果:")
    direction_stats = df.groupby('line_direction').agg({
        'total_goals': ['count', 'mean'],
        'match_id': lambda x: (df.loc[x.index, 'total_goals'] > 2.5).mean()
    }).round(3)
    
    for direction in ['升盘', '平盘', '降盘']:
        direction_data = df[df['line_direction'] == direction]
        if not direction_data.empty:
            count = len(direction_data)
            avg_goals = direction_data['total_goals'].mean()
            over_rate = (direction_data['total_goals'] > 2.5).mean()
            
            logger.info(f"   {direction} ({count} 场): 平均进球 {avg_goals:.2f}, 大2.5球率 {over_rate:.1%}")
    
    # 3. 市场情绪分析
    logger.info(f"\n💭 市场情绪与结果:")
    for sentiment in ['FAVOR_OVER', 'NEUTRAL', 'FAVOR_UNDER']:
        sentiment_data = df[df['market_sentiment'] == sentiment]
        if not sentiment_data.empty:
            count = len(sentiment_data)
            avg_goals = sentiment_data['total_goals'].mean()
            over_rate = (sentiment_data['total_goals'] > 2.5).mean()
            
            logger.info(f"   {sentiment} ({count} 场): 平均进球 {avg_goals:.2f}, 大2.5球率 {over_rate:.1%}")
    
    # 4. 具体盘口分析
    logger.info(f"\n📈 具体盘口分析:")
    
    for line_value in [2.5, 3.5]:
        line_data = df[abs(df['opening_line'] - line_value) < 0.1].copy()
        
        if len(line_data) < 10:
            continue
        
        logger.info(f"\n   盘口 {line_value} ({len(line_data)} 场):")
        
        # 基准命中率
        baseline_rate = (line_data['total_goals'] > line_value).mean()
        logger.info(f"     基准大球命中率: {baseline_rate:.1%}")
        
        # 升盘分析
        up_data = line_data[line_data['line_change'] > 0.1]
        if not up_data.empty:
            up_rate = (up_data['total_goals'] > line_value).mean()
            improvement = up_rate - baseline_rate
            logger.info(f"     🔺 升盘 ({len(up_data)} 场): {up_rate:.1%} (vs基准 {improvement:+.1%})")
        
        # 降盘分析
        down_data = line_data[line_data['line_change'] < -0.1]
        if not down_data.empty:
            down_rate = (down_data['total_goals'] > line_value).mean()
            improvement = down_rate - baseline_rate
            logger.info(f"     🔻 降盘 ({len(down_data)} 场): {down_rate:.1%} (vs基准 {improvement:+.1%})")
        
        # 平盘分析
        stable_data = line_data[abs(line_data['line_change']) <= 0.1]
        if not stable_data.empty:
            stable_rate = (stable_data['total_goals'] > line_value).mean()
            improvement = stable_rate - baseline_rate
            logger.info(f"     ➡️ 平盘 ({len(stable_data)} 场): {stable_rate:.1%} (vs基准 {improvement:+.1%})")
    
    # 5. 强信号分析
    logger.info(f"\n⭐ 强信号组合分析:")
    
    # 强大球信号: 升盘 + 市场倾向大球
    strong_over = df[
        (df['line_change'] > 0.2) & 
        (df['market_sentiment'] == 'FAVOR_OVER')
    ]
    
    if not strong_over.empty:
        over_rate = (strong_over['total_goals'] > 2.5).mean()
        avg_goals = strong_over['total_goals'].mean()
        logger.info(f"   🔥 强大球信号 (升盘>0.2 + 市场倾向大球): {len(strong_over)} 场")
        logger.info(f"      大2.5球命中率: {over_rate:.1%}, 平均进球: {avg_goals:.2f}")
    
    # 强小球信号: 降盘 + 市场倾向小球
    strong_under = df[
        (df['line_change'] < -0.2) & 
        (df['market_sentiment'] == 'FAVOR_UNDER')
    ]
    
    if not strong_under.empty:
        under_rate = (strong_under['total_goals'] <= 2.5).mean()
        avg_goals = strong_under['total_goals'].mean()
        logger.info(f"   🧊 强小球信号 (降盘>0.2 + 市场倾向小球): {len(strong_under)} 场")
        logger.info(f"      小2.5球命中率: {under_rate:.1%}, 平均进球: {avg_goals:.2f}")
    
    # 6. 相关性分析
    logger.info(f"\n📊 相关性分析:")
    
    line_corr = df['line_change'].corr(df['total_goals'])
    over_odds_corr = df['over_odds_change'].corr(df['total_goals'])
    
    logger.info(f"   盘口变化 vs 进球数相关性: {line_corr:.3f}")
    logger.info(f"   大球赔率变化 vs 进球数相关性: {over_odds_corr:.3f}")
    
    # 7. 预测价值总结
    logger.info(f"\n🎯 预测价值总结:")
    
    # 升盘预测准确性
    up_games = df[df['line_change'] > 0.1]
    if not up_games.empty:
        up_accuracy = (up_games['total_goals'] > 2.5).mean()
        baseline = (df['total_goals'] > 2.5).mean()
        improvement = up_accuracy - baseline
        logger.info(f"   升盘预测大球: 准确率 {up_accuracy:.1%} (vs基准 {improvement:+.1%})")
    
    # 降盘预测准确性
    down_games = df[df['line_change'] < -0.1]
    if not down_games.empty:
        down_accuracy = (down_games['total_goals'] <= 2.5).mean()
        baseline = (df['total_goals'] <= 2.5).mean()
        improvement = down_accuracy - baseline
        logger.info(f"   降盘预测小球: 准确率 {down_accuracy:.1%} (vs基准 {improvement:+.1%})")
    
    # 市场情绪准确性
    favor_over_games = df[df['market_sentiment'] == 'FAVOR_OVER']
    if not favor_over_games.empty:
        favor_over_accuracy = (favor_over_games['total_goals'] > 2.5).mean()
        baseline = (df['total_goals'] > 2.5).mean()
        improvement = favor_over_accuracy - baseline
        logger.info(f"   市场倾向大球: 准确率 {favor_over_accuracy:.1%} (vs基准 {improvement:+.1%})")
    
    return df

def main():
    """主函数"""
    logger.info("开始赔率变化与结果关联分析")
    
    try:
        # 获取数据
        df = get_odds_and_results()
        
        if not df.empty:
            # 分析关联性
            analyze_odds_changes_vs_results(df)
        
        logger.info("\n✅ 分析完成")
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        raise

if __name__ == "__main__":
    main()
