#!/usr/bin/env python3
"""
足球预测Web服务器
基于Python内置HTTP服务器，提供完整的Web界面
"""

import http.server
import socketserver
import json
import urllib.parse
from decimal import Decimal
import numpy as np
import logging
import pymysql
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import joblib
import os
import threading

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WebServer")

class PredictionEngine:
    """预测引擎"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.models_cache = {}
        self.load_models()
    
    def load_models(self):
        """加载模型"""
        logger.info("加载预测模型...")
        
        model_dirs = {
            'goals': 'saved_models/robust_models/goals',
            'match_result': 'saved_models/robust_models/match_result',
            'asian_handicap': 'saved_models/robust_models/asian_handicap',
            'corners': 'saved_models/robust_models/corners'
        }
        
        loaded_count = 0
        
        for category, model_dir in model_dirs.items():
            if not os.path.exists(model_dir):
                continue
                
            self.models_cache[category] = {}
            
            for filename in os.listdir(model_dir):
                if filename.endswith('_robust.joblib'):
                    model_path = os.path.join(model_dir, filename)
                    try:
                        model_info = joblib.load(model_path)
                        model_key = self._parse_model_key(filename)
                        self.models_cache[category][model_key] = model_info
                        loaded_count += 1
                    except Exception as e:
                        logger.error(f"加载模型失败 {filename}: {e}")
        
        logger.info(f"成功加载 {loaded_count} 个模型")
    
    def _parse_model_key(self, filename: str) -> str:
        """解析模型键值"""
        parts = filename.replace('_robust.joblib', '').split('_')
        if len(parts) >= 4:
            return '_'.join(parts[1:4])
        return filename.replace('_robust.joblib', '')
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def get_matches_to_predict(self, start_datetime: str = None, end_datetime: str = None) -> List[Dict]:
        """获取要预测的比赛"""
        if start_datetime is None:
            # 默认从今天00:00:00开始
            start_datetime = datetime.now().strftime('%Y-%m-%d 00:00:00')

        if end_datetime is None:
            # 默认到明天23:59:59结束
            tomorrow = datetime.now() + timedelta(days=1)
            end_datetime = tomorrow.strftime('%Y-%m-%d 23:59:59')
        
        try:
            with self.get_connection() as conn:
                query = """
                SELECT 
                    m.id as match_id,
                    m.league_id,
                    l.name as league_name,
                    l.priority as league_priority,
                    m.home_team_name,
                    m.away_team_name,
                    m.match_datetime,
                    m.status,
                    
                    -- 初盘赔率
                    o.goals_line_1,
                    o.goals_over_odds_1,
                    o.goals_under_odds_1,
                    o.asian_handicap_line_1,
                    o.asian_home_odds_1,
                    o.asian_away_odds_1,
                    o.corners_line,
                    o.corners_over_odds,
                    o.corners_under_odds,
                    o.home_win_odds,
                    o.draw_odds,
                    o.away_win_odds
                    
                FROM matches m
                JOIN leagues l ON m.league_id = l.id
                LEFT JOIN odds o ON m.id = o.match_id 
                                  AND o.odds_type = 'OPENING' 
                                  AND o.bookmaker = '365bet'
                
                WHERE m.match_datetime BETWEEN %s AND %s
                # AND m.status <> 'FINISHED'
                AND (l.priority IN (0, 1, 99) OR l.priority BETWEEN 2 AND 98)
                ORDER BY m.match_datetime ASC
                """
                
                cursor = conn.cursor()
                cursor.execute(query, [start_datetime, end_datetime])
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                # 转换为字典列表
                matches = []
                for row in rows:
                    match_dict = dict(zip(columns, row))
                    # 转换特殊类型为JSON可序列化类型
                    match_dict = self._convert_to_json_serializable(match_dict)
                    matches.append(match_dict)

                return matches
        
        except Exception as e:
            logger.error(f"获取比赛数据失败: {e}")
            return []

    def _convert_to_json_serializable(self, data):
        """转换数据为JSON可序列化格式"""
        if isinstance(data, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._convert_to_json_serializable(item) for item in data]
        elif isinstance(data, Decimal):
            return float(data)
        elif hasattr(data, 'isoformat'):  # datetime objects
            # 格式化为更友好的日期时间格式
            return data.strftime('%Y-%m-%d %H:%M:%S')
        elif hasattr(data, 'strftime'):  # date objects
            return data.strftime('%Y-%m-%d')
        elif data is None:
            return None
        else:
            return data
    
    def save_prediction_to_db(self, match_id: int, category: str, prediction_data: Dict):
        """保存预测到数据库"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                insert_sql = """
                INSERT INTO web_predictions
                (match_id, category, handicap_line, predicted_result, predicted_probability,
                 confidence_level, model_used, prediction_description)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """

                cursor.execute(insert_sql, (
                    match_id,
                    category,
                    prediction_data.get('handicap_line'),
                    prediction_data.get('prediction'),
                    prediction_data.get('probability'),
                    prediction_data.get('confidence'),
                    prediction_data.get('model_used', 'simulated'),
                    prediction_data.get('description')
                ))
                conn.commit()

                return cursor.lastrowid

        except Exception as e:
            logger.error(f"保存预测失败: {e}")
            return None

    def predict_match(self, match_data: Dict, save_to_db: bool = False) -> Dict:
        """预测单场比赛"""
        try:
            match_id = match_data.get('match_id')

            # 简化预测逻辑
            predictions = {
                'match_info': {
                    'match_id': match_id,
                    'home_team': match_data.get('home_team_name', 'Home'),
                    'away_team': match_data.get('away_team_name', 'Away'),
                    'league_name': match_data.get('league_name', 'Unknown'),
                    'match_datetime': str(match_data.get('match_datetime', '')),
                },
                'predictions': {}
            }
            
            # 模拟预测结果
            categories = [
                ('goals', match_data.get('goals_line_1')),
                ('match_result', '1X2'),
                ('asian_handicap', match_data.get('asian_handicap_line_1')),
                ('corners', match_data.get('corners_line'))
            ]

            for category, handicap_line in categories:
                if handicap_line is None or (isinstance(handicap_line, float) and np.isnan(handicap_line)):
                    continue

                # 简化：生成模拟预测
                probability = np.random.uniform(0.5, 0.9)
                prediction = 1 if probability > 0.5 else 0
                confidence = 'HIGH' if probability > 0.75 else ('MEDIUM' if probability > 0.65 else 'LOW')

                if category == 'goals':
                    description = f"{'大球' if prediction == 1 else '小球'} {handicap_line}"
                elif category == 'match_result':
                    prediction = np.random.randint(0, 3)  # 0=主胜, 1=平局, 2=客胜
                    results = ['主胜', '平局', '客胜']
                    description = results[prediction]
                elif category == 'asian_handicap':
                    description = f"{'主队赢盘' if prediction == 1 else '主队输盘'} {handicap_line}"
                elif category == 'corners':
                    description = f"{'大角球' if prediction == 1 else '小角球'} {handicap_line}"

                prediction_data = {
                    'success': True,
                    'prediction': prediction,
                    'probability': probability,
                    'confidence': confidence,
                    'description': description,
                    'handicap_line': handicap_line,
                    'model_used': f'simulated_{category}_model'
                }

                predictions['predictions'][category] = prediction_data

                # 保存到数据库
                if save_to_db and match_id:
                    self.save_prediction_to_db(match_id, category, prediction_data)
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {'error': str(e)}

    def verify_predictions(self, start_datetime: str = None, end_datetime: str = None) -> Dict:
        """验证预测结果"""
        try:
            with self.get_connection() as conn:
                # 获取需要验证的预测
                query = """
                SELECT
                    p.id as prediction_id,
                    p.match_id,
                    p.category,
                    p.handicap_line,
                    p.predicted_result,
                    p.predicted_probability,
                    p.confidence_level,
                    m.home_team_name,
                    m.away_team_name,
                    m.match_datetime,
                    m.status,
                    ms.home_goals,
                    ms.away_goals,
                    ms.total_goals,
                    ms.total_corners
                FROM web_predictions p
                JOIN matches m ON p.match_id = m.id
                LEFT JOIN match_stats ms ON m.id = ms.match_id
                WHERE p.is_correct IS NULL
                AND m.status = 'FINISHED'
                AND ms.home_goals IS NOT NULL
                AND ms.away_goals IS NOT NULL
                """

                params = []
                if start_datetime and end_datetime:
                    query += " AND m.match_datetime BETWEEN %s AND %s"
                    params.extend([start_datetime, end_datetime])

                query += " ORDER BY m.match_datetime DESC"

                cursor = conn.cursor()
                cursor.execute(query, params)
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                logger.info(f"验证预测: 时间段 {start_datetime} 到 {end_datetime}")
                logger.info(f"找到 {len(rows)} 条需要验证的预测记录")

                if not rows:
                    return {
                        'success': True,
                        'verified_count': 0,
                        'message': '没有需要验证的预测'
                    }

                verified_count = 0
                correct_count = 0

                for row in rows:
                    row_dict = dict(zip(columns, row))
                    actual_result = self._calculate_actual_result(row_dict)

                    if actual_result is not None:
                        is_correct = (actual_result == row_dict['predicted_result'])

                        # 更新预测结果
                        update_sql = """
                        UPDATE web_predictions
                        SET actual_result = %s, is_correct = %s, verified_at = NOW()
                        WHERE id = %s
                        """

                        cursor.execute(update_sql, (actual_result, is_correct, row_dict['prediction_id']))

                        verified_count += 1
                        if is_correct:
                            correct_count += 1

                conn.commit()

                accuracy = correct_count / verified_count if verified_count > 0 else 0

                logger.info(f"✅ 验证完成: 验证了 {verified_count} 个预测")
                logger.info(f"✅ 正确预测: {correct_count} 个")
                logger.info(f"✅ 准确率: {accuracy:.1%}")

                return {
                    'success': True,
                    'verified_count': verified_count,
                    'correct_count': correct_count,
                    'accuracy': accuracy,
                    'message': f'验证完成：{verified_count}个预测，准确率{accuracy:.1%}'
                }

        except Exception as e:
            logger.error(f"验证预测失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _calculate_actual_result(self, row) -> Optional[int]:
        """计算实际结果"""
        category = row['category']

        try:
            if category == 'goals':
                handicap_line = float(row['handicap_line'])
                total_goals = row['total_goals']
                return 1 if total_goals > handicap_line else 0

            elif category == 'match_result':
                home_goals = row['home_goals']
                away_goals = row['away_goals']
                if home_goals > away_goals:
                    return 0  # 主胜
                elif home_goals == away_goals:
                    return 1  # 平局
                else:
                    return 2  # 客胜

            elif category == 'asian_handicap':
                handicap_line = float(row['handicap_line'])
                home_goals = row['home_goals']
                away_goals = row['away_goals']
                home_adjusted = home_goals + handicap_line
                return 1 if home_adjusted > away_goals else 0

            elif category == 'corners':
                handicap_line = float(row['handicap_line'])
                total_corners = row['total_corners']
                if total_corners is not None:
                    return 1 if total_corners > handicap_line else 0

        except (ValueError, TypeError):
            pass

        return None

    def get_prediction_stats(self) -> Dict:
        """获取预测统计"""
        try:
            with self.get_connection() as conn:
                query = """
                SELECT
                    category,
                    confidence_level,
                    COUNT(*) as total_predictions,
                    SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_predictions,
                    AVG(CASE WHEN is_correct = 1 THEN 1.0 ELSE 0.0 END) as accuracy_rate,
                    COUNT(CASE WHEN verified_at IS NOT NULL THEN 1 END) as verified_count
                FROM web_predictions
                WHERE is_correct IS NOT NULL
                GROUP BY category, confidence_level
                ORDER BY category, confidence_level
                """

                cursor = conn.cursor()
                cursor.execute(query)
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                stats = []
                for row in rows:
                    stat_dict = dict(zip(columns, row))
                    stat_dict = self._convert_to_json_serializable(stat_dict)
                    stats.append(stat_dict)

                return {
                    'success': True,
                    'stats': stats
                }

        except Exception as e:
            logger.error(f"获取统计失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_unified_matches(self, start_datetime: str = None, end_datetime: str = None) -> Dict:
        """获取统一的比赛和预测信息"""
        try:
            with self.get_connection() as conn:
                # 统一查询比赛和预测信息
                query = """
                SELECT
                    m.id as match_id,
                    m.home_team_name,
                    m.away_team_name,
                    m.match_datetime,
                    m.status,
                    l.name as league_name,
                    l.priority as league_priority,

                    -- 比赛结果
                    ms.home_goals,
                    ms.away_goals,
                    ms.total_goals,
                    ms.total_corners,

                    -- 预测信息
                    p.id as prediction_id,
                    p.category,
                    p.handicap_line,
                    p.predicted_result,
                    p.predicted_probability,
                    p.confidence_level,
                    p.prediction_description,
                    p.actual_result,
                    p.is_correct,
                    p.verified_at,
                    p.created_at as prediction_time

                FROM matches m
                JOIN leagues l ON m.league_id = l.id
                LEFT JOIN match_stats ms ON m.id = ms.match_id
                LEFT JOIN web_predictions p ON m.id = p.match_id
                WHERE 1=1
                """

                params = []
                if start_datetime and end_datetime:
                    query += " AND m.match_datetime BETWEEN %s AND %s"
                    params.extend([start_datetime, end_datetime])

                query += " ORDER BY m.match_datetime DESC, p.created_at DESC"

                cursor = conn.cursor()
                cursor.execute(query, params)
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                logger.info(f"统一查询: 找到 {len(rows)} 条记录")

                # 按比赛分组
                matches_dict = {}

                for row in rows:
                    row_dict = dict(zip(columns, row))
                    row_dict = self._convert_to_json_serializable(row_dict)

                    match_id = row_dict['match_id']

                    if match_id not in matches_dict:
                        matches_dict[match_id] = {
                            'match_info': {
                                'match_id': match_id,
                                'home_team': row_dict['home_team_name'],
                                'away_team': row_dict['away_team_name'],
                                'league_name': row_dict['league_name'],
                                'league_priority': row_dict['league_priority'],
                                'match_datetime': row_dict['match_datetime'],
                                'status': row_dict['status'],
                                'home_goals': row_dict['home_goals'],
                                'away_goals': row_dict['away_goals'],
                                'total_goals': row_dict['total_goals'],
                                'total_corners': row_dict['total_corners']
                            },
                            'predictions': {},
                            'summary': {
                                'total_predictions': 0,
                                'correct_predictions': 0,
                                'verified_predictions': 0,
                                'has_predictions': False
                            }
                        }

                    # 添加预测记录
                    if row_dict['prediction_id']:
                        category = row_dict['category']
                        matches_dict[match_id]['predictions'][category] = {
                            'prediction_id': row_dict['prediction_id'],
                            'handicap_line': row_dict['handicap_line'],
                            'predicted_result': row_dict['predicted_result'],
                            'predicted_probability': row_dict['predicted_probability'],
                            'confidence_level': row_dict['confidence_level'],
                            'description': row_dict['prediction_description'],
                            'actual_result': row_dict['actual_result'],
                            'is_correct': row_dict['is_correct'],
                            'verified_at': row_dict['verified_at'],
                            'prediction_time': row_dict['prediction_time']
                        }

                        # 更新统计
                        matches_dict[match_id]['summary']['total_predictions'] += 1
                        matches_dict[match_id]['summary']['has_predictions'] = True
                        if row_dict['is_correct'] is not None:
                            matches_dict[match_id]['summary']['verified_predictions'] += 1
                            if row_dict['is_correct']:
                                matches_dict[match_id]['summary']['correct_predictions'] += 1

                # 转换为列表并排序
                unified_matches = list(matches_dict.values())
                unified_matches.sort(key=lambda x: x['match_info']['match_datetime'], reverse=True)

                return {
                    'success': True,
                    'count': len(unified_matches),
                    'matches': unified_matches
                }

        except Exception as e:
            logger.error(f"获取统一数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_predicted_matches(self, start_datetime: str = None, end_datetime: str = None) -> Dict:
        """获取已预测的比赛记录"""
        try:
            with self.get_connection() as conn:
                query = """
                SELECT
                    m.id as match_id,
                    m.home_team_name,
                    m.away_team_name,
                    m.match_datetime,
                    m.status,
                    l.name as league_name,
                    l.priority as league_priority,

                    -- 预测记录
                    p.id as prediction_id,
                    p.category,
                    p.handicap_line,
                    p.predicted_result,
                    p.predicted_probability,
                    p.confidence_level,
                    p.prediction_description,
                    p.actual_result,
                    p.is_correct,
                    p.verified_at,
                    p.created_at as prediction_time,

                    -- 实际比赛结果
                    ms.home_goals,
                    ms.away_goals,
                    ms.total_goals,
                    ms.total_corners

                FROM web_predictions p
                JOIN matches m ON p.match_id = m.id
                JOIN leagues l ON m.league_id = l.id
                LEFT JOIN match_stats ms ON m.id = ms.match_id
                WHERE 1=1
                """

                params = []
                if start_datetime and end_datetime:
                    query += " AND m.match_datetime BETWEEN %s AND %s"
                    params.extend([start_datetime, end_datetime])

                query += " ORDER BY m.match_datetime DESC, p.created_at DESC"

                cursor = conn.cursor()
                cursor.execute(query, params)
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                logger.info(f"查询预测记录: 找到 {len(rows)} 条记录")

                # 按比赛分组预测记录
                matches_dict = {}

                for row in rows:
                    row_dict = dict(zip(columns, row))
                    row_dict = self._convert_to_json_serializable(row_dict)

                    match_id = row_dict['match_id']

                    if match_id not in matches_dict:
                        matches_dict[match_id] = {
                            'match_info': {
                                'match_id': match_id,
                                'home_team': row_dict['home_team_name'],
                                'away_team': row_dict['away_team_name'],
                                'league_name': row_dict['league_name'],
                                'match_datetime': row_dict['match_datetime'],
                                'status': row_dict['status'],
                                'home_goals': row_dict['home_goals'],
                                'away_goals': row_dict['away_goals'],
                                'total_goals': row_dict['total_goals'],
                                'total_corners': row_dict['total_corners']
                            },
                            'predictions': {},
                            'summary': {
                                'total_predictions': 0,
                                'correct_predictions': 0,
                                'verified_predictions': 0
                            }
                        }

                    # 添加预测记录
                    category = row_dict['category']
                    matches_dict[match_id]['predictions'][category] = {
                        'prediction_id': row_dict['prediction_id'],
                        'handicap_line': row_dict['handicap_line'],
                        'predicted_result': row_dict['predicted_result'],
                        'predicted_probability': row_dict['predicted_probability'],
                        'confidence_level': row_dict['confidence_level'],
                        'description': row_dict['prediction_description'],
                        'actual_result': row_dict['actual_result'],
                        'is_correct': row_dict['is_correct'],
                        'verified_at': row_dict['verified_at'],
                        'prediction_time': row_dict['prediction_time']
                    }

                    # 更新统计
                    matches_dict[match_id]['summary']['total_predictions'] += 1
                    if row_dict['is_correct'] is not None:
                        matches_dict[match_id]['summary']['verified_predictions'] += 1
                        if row_dict['is_correct']:
                            matches_dict[match_id]['summary']['correct_predictions'] += 1

                # 转换为列表
                predicted_matches = list(matches_dict.values())

                return {
                    'success': True,
                    'count': len(predicted_matches),
                    'matches': predicted_matches,
                    'predictions': predicted_matches  # 兼容前端期望的字段名
                }

        except Exception as e:
            logger.error(f"获取预测记录失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# 全局预测引擎
prediction_engine = PredictionEngine()

class FootballPredictionHandler(http.server.SimpleHTTPRequestHandler):
    """足球预测HTTP处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_main_page()
        elif self.path.startswith('/api/matches'):
            self.serve_matches_api()
        elif self.path.startswith('/api/predicted-matches'):  # 必须在 /api/predict 之前
            self.serve_predicted_matches_api()
        elif self.path.startswith('/api/predict'):
            self.serve_predict_api()
        elif self.path.startswith('/api/verify'):
            self.serve_verify_api()
        elif self.path.startswith('/api/stats'):
            self.serve_stats_api()
        elif self.path.startswith('/api/test-predictions'):
            self.serve_test_predictions_api()
        elif self.path.startswith('/api/debug-predictions'):
            self.serve_debug_predictions_api()
        elif self.path.startswith('/api/unified-matches'):
            self.serve_unified_matches_api()
        else:
            self.send_error(404, "Page not found")
    
    def serve_main_page(self):
        """提供主页"""
        html_content = self.get_main_html()
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', len(html_content.encode('utf-8')))
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_matches_api(self):
        """提供比赛列表API"""
        # 解析查询参数
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        start_datetime = query_params.get('start_datetime', [None])[0]
        end_datetime = query_params.get('end_datetime', [None])[0]

        try:
            matches = prediction_engine.get_matches_to_predict(start_datetime, end_datetime)
            
            response_data = {
                'success': True,
                'start_datetime': start_datetime or datetime.now().strftime('%Y-%m-%d 00:00:00'),
                'end_datetime': end_datetime or (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d 23:59:59'),
                'count': len(matches),
                'matches': matches
            }
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)
    
    def serve_predict_api(self):
        """提供预测API"""
        logger.info("🎯 调用预测API - /api/predict (会创建新预测)")

        # 解析查询参数
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        start_datetime = query_params.get('start_datetime', [None])[0]
        end_datetime = query_params.get('end_datetime', [None])[0]

        logger.info(f"预测时间段: {start_datetime} 到 {end_datetime}")

        try:
            matches = prediction_engine.get_matches_to_predict(start_datetime, end_datetime)
            logger.info(f"找到 {len(matches)} 场待预测比赛")
            predictions = []
            
            for match in matches:  # 移除数量限制
                # 检查是否有有效的盘口数据
                if self._has_valid_odds(match):
                    prediction_result = prediction_engine.predict_match(match, save_to_db=True)
                    predictions.append(prediction_result)
                else:
                    logger.info(f"跳过比赛 {match.get('home_team_name')} vs {match.get('away_team_name')} - 缺少盘口数据")
            
            response_data = {
                'success': True,
                'start_datetime': start_datetime or datetime.now().strftime('%Y-%m-%d 00:00:00'),
                'end_datetime': end_datetime or (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d 23:59:59'),
                'count': len(predictions),
                'predictions': predictions
            }
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def _has_valid_odds(self, match_data: Dict) -> bool:
        """检查比赛是否有有效的盘口数据"""
        # 检查各类别是否有盘口数据
        has_goals = match_data.get('goals_line_1') is not None and not (isinstance(match_data.get('goals_line_1'), float) and np.isnan(match_data.get('goals_line_1')))
        has_1x2 = (match_data.get('home_win_odds') is not None and
                   match_data.get('draw_odds') is not None and
                   match_data.get('away_win_odds') is not None)
        has_asian = match_data.get('asian_handicap_line_1') is not None and not (isinstance(match_data.get('asian_handicap_line_1'), float) and np.isnan(match_data.get('asian_handicap_line_1')))
        has_corners = match_data.get('corners_line') is not None and not (isinstance(match_data.get('corners_line'), float) and np.isnan(match_data.get('corners_line')))

        # 至少要有一个类别的盘口数据
        return has_goals or has_1x2 or has_asian or has_corners

    def serve_verify_api(self):
        """提供验证预测API"""
        # 解析查询参数
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        start_datetime = query_params.get('start_datetime', [None])[0]
        end_datetime = query_params.get('end_datetime', [None])[0]

        try:
            result = prediction_engine.verify_predictions(start_datetime, end_datetime)
            self.send_json_response(result)

        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def serve_stats_api(self):
        """提供预测统计API"""
        try:
            result = prediction_engine.get_prediction_stats()
            self.send_json_response(result)

        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def serve_predicted_matches_api(self):
        """提供已预测比赛API"""
        logger.info("🔍 调用预测记录查询API - /api/predicted-matches")

        # 解析查询参数
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        start_datetime = query_params.get('start_datetime', [None])[0]
        end_datetime = query_params.get('end_datetime', [None])[0]

        logger.info(f"查询时间段: {start_datetime} 到 {end_datetime}")

        try:
            result = prediction_engine.get_predicted_matches(start_datetime, end_datetime)
            logger.info(f"查询结果: 找到 {result.get('count', 0)} 场已预测比赛")
            self.send_json_response(result)

        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def serve_test_predictions_api(self):
        """测试预测数据API"""
        try:
            with prediction_engine.get_connection() as conn:
                cursor = conn.cursor()

                # 简单查询预测表
                cursor.execute("SELECT COUNT(*) as count FROM web_predictions")
                total_count = cursor.fetchone()[0]

                cursor.execute("SELECT * FROM web_predictions ORDER BY created_at DESC LIMIT 5")
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                recent_predictions = []
                for row in rows:
                    pred_dict = dict(zip(columns, row))
                    pred_dict = prediction_engine._convert_to_json_serializable(pred_dict)
                    recent_predictions.append(pred_dict)

                self.send_json_response({
                    'success': True,
                    'total_predictions': total_count,
                    'recent_predictions': recent_predictions
                })

        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def serve_debug_predictions_api(self):
        """调试预测数据API"""
        try:
            with prediction_engine.get_connection() as conn:
                cursor = conn.cursor()

                # 查询预测表的基本信息
                cursor.execute("SELECT COUNT(*) as total FROM web_predictions")
                total_count = cursor.fetchone()[0]

                # 查询验证状态分布
                cursor.execute("""
                    SELECT
                        is_correct,
                        COUNT(*) as count
                    FROM web_predictions
                    GROUP BY is_correct
                """)
                verification_stats_raw = cursor.fetchall()
                verification_stats = []
                for row in verification_stats_raw:
                    verification_stats.append({
                        'is_correct': row[0],
                        'count': int(row[1])
                    })

                # 查询最近的预测记录（包含所有字段）
                cursor.execute("""
                    SELECT
                        p.id, p.match_id, p.category, p.predicted_result,
                        p.actual_result, p.is_correct, p.verified_at,
                        m.home_team_name, m.away_team_name, m.status
                    FROM web_predictions p
                    JOIN matches m ON p.match_id = m.id
                    ORDER BY p.created_at DESC
                    LIMIT 10
                """)
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()

                recent_predictions = []
                for row in rows:
                    pred_dict = dict(zip(columns, row))
                    pred_dict = prediction_engine._convert_to_json_serializable(pred_dict)
                    recent_predictions.append(pred_dict)

                # 查询验证状态详情
                cursor.execute("""
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN is_correct IS NOT NULL THEN 1 ELSE 0 END) as verified,
                        SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct
                    FROM web_predictions
                """)
                stats_raw = cursor.fetchone()
                stats = {
                    'total': int(stats_raw[0]) if stats_raw[0] else 0,
                    'verified': int(stats_raw[1]) if stats_raw[1] else 0,
                    'correct': int(stats_raw[2]) if stats_raw[2] else 0
                }

                self.send_json_response({
                    'success': True,
                    'total_predictions': int(total_count),
                    'verification_stats': verification_stats,
                    'overall_stats': stats,
                    'recent_predictions': recent_predictions
                })

        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def serve_unified_matches_api(self):
        """统一比赛和预测API"""
        logger.info("📋 调用统一比赛预测API - /api/unified-matches")

        # 解析查询参数
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        start_datetime = query_params.get('start_datetime', [None])[0]
        end_datetime = query_params.get('end_datetime', [None])[0]

        try:
            result = prediction_engine.get_unified_matches(start_datetime, end_datetime)
            self.send_json_response(result)

        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)

    def send_json_response(self, data: Dict, status_code: int = 200):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', len(json_data.encode('utf-8')))
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def get_main_html(self) -> str:
        """获取主页HTML"""
        today = datetime.now().strftime('%Y-%m-%d')
        today_start = datetime.now().strftime('%Y-%m-%dT00:00')
        tomorrow_end = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%dT23:59')

        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚽ 足球预测系统</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .controls {{
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }}
        
        .control-group {{
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }}
        
        .control-group label {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }}
        
        .control-group input[type="date"],
        .control-group input[type="datetime-local"] {{
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
            min-width: 200px;
        }}

        .control-group input[type="date"]:focus,
        .control-group input[type="datetime-local"]:focus {{
            outline: none;
            border-color: #667eea;
        }}

        .datetime-group {{
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }}

        .datetime-separator {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }}
        
        .btn {{
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }}
        
        .btn-primary {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }}
        
        .btn-primary:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }}
        
        .btn-success {{
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }}
        
        .btn-success:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }}

        .btn-warning {{
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }}

        .btn-warning:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }}

        .btn-info {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }}

        .btn-info:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }}

        .btn-secondary {{
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }}

        .btn-secondary:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }}
        
        .results {{
            padding: 30px;
            min-height: 400px;
        }}
        
        .loading {{
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-size: 1.2em;
        }}
        
        .loading::before {{
            content: "⚽";
            display: block;
            font-size: 3em;
            margin-bottom: 20px;
            animation: bounce 1s infinite;
        }}
        
        @keyframes bounce {{
            0%, 20%, 50%, 80%, 100% {{ transform: translateY(0); }}
            40% {{ transform: translateY(-10px); }}
            60% {{ transform: translateY(-5px); }}
        }}
        
        .match-card {{
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            margin: 15px 0;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }}
        
        .match-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }}
        
        .match-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }}
        
        .match-teams {{
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .match-info {{
            color: #6c757d;
            font-size: 0.9em;
        }}
        
        .predictions-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}
        
        .prediction {{
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
            transition: transform 0.2s;
        }}
        
        .prediction:hover {{
            transform: scale(1.02);
        }}
        
        .prediction-high {{
            background: linear-gradient(135deg, #d5f4e6 0%, #c8e6c9 100%);
            border-left-color: #27ae60;
        }}
        
        .prediction-medium {{
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-left-color: #f39c12;
        }}
        
        .prediction-low {{
            background: linear-gradient(135deg, #f8d7da 0%, #fab1a0 100%);
            border-left-color: #e74c3c;
        }}
        
        .prediction-title {{
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .prediction-desc {{
            font-size: 1.1em;
            margin-bottom: 5px;
        }}
        
        .prediction-confidence {{
            font-size: 0.9em;
            opacity: 0.8;
        }}
        
        .error {{
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }}
        
        .success {{
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        
        @media (max-width: 768px) {{
            .control-group {{
                flex-direction: column;
                align-items: stretch;
            }}
            
            .match-header {{
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }}
            
            .predictions-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚽ 足球预测系统</h1>
            <p>基于机器学习的多类别足球比赛预测 | 30个专用模型 | 369,558场训练数据</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>🕒 预测时间段:</label>
                <div class="datetime-group">
                    <div>
                        <label for="start-datetime">开始时间:</label>
                        <input type="datetime-local" id="start-datetime" value="{today_start}">
                    </div>
                    <div class="datetime-separator">至</div>
                    <div>
                        <label for="end-datetime">结束时间:</label>
                        <input type="datetime-local" id="end-datetime" value="{tomorrow_end}">
                    </div>
                </div>
            </div>
            <div class="control-group" style="margin-top: 15px;">
                <button class="btn btn-success" onclick="startPrediction()">🎯 开始预测</button>
                <button class="btn btn-warning" onclick="verifyPredictions()">✅ 验证预测</button>
                <button class="btn btn-info" onclick="showStats()">📊 预测统计</button>
                <button class="btn btn-primary" onclick="setDefaultTime()">🔄 重置时间</button>
            </div>
        </div>
        
        <div class="results" id="results">
            <div class="loading">正在加载系统...</div>
        </div>
    </div>

    <script>
        // 页面加载完成后自动加载统一数据
        window.onload = function() {{
            loadUnifiedMatches();
        }};

        function formatDateTime(dateTimeStr) {{
            if (!dateTimeStr) return 'N/A';

            try {{
                const date = new Date(dateTimeStr);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');

                return `${{year}}-${{month}}-${{day}} ${{hours}}:${{minutes}}`;
            }} catch (e) {{
                return dateTimeStr;
            }}
        }}

        function setDefaultTime() {{
            const now = new Date();
            const today = now.toISOString().slice(0, 10);
            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString().slice(0, 10);

            document.getElementById('start-datetime').value = `${{today}}T00:00`;
            document.getElementById('end-datetime').value = `${{tomorrow}}T23:59`;
        }}

        function getDateTimeParams() {{
            const startDateTime = document.getElementById('start-datetime').value;
            const endDateTime = document.getElementById('end-datetime').value;

            // 转换为数据库格式 (YYYY-MM-DD HH:MM:SS)
            const startFormatted = startDateTime.replace('T', ' ') + ':00';
            const endFormatted = endDateTime.replace('T', ' ') + ':59';

            return {{
                start_datetime: startFormatted,
                end_datetime: endFormatted
            }};
        }}

        function loadMatches() {{
            const params = getDateTimeParams();
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">正在加载比赛...</div>';

            const url = `/api/matches?start_datetime=${{encodeURIComponent(params.start_datetime)}}&end_datetime=${{encodeURIComponent(params.end_datetime)}}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {{
                    if (data.success && data.matches && data.matches.length > 0) {{
                        displayMatches(data.matches, data.start_datetime, data.end_datetime);
                    }} else {{
                        resultsDiv.innerHTML = '<div class="error">❌ 没有找到比赛数据</div>';
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 加载失败: ${{error.message}}</div>`;
                }});
        }}

        function testPredictions() {{
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">🔧 正在测试预测数据...</div>';

            fetch('/api/test-predictions')
                .then(response => response.json())
                .then(data => {{
                    console.log('测试数据:', data);

                    if (data.success) {{
                        let html = `
                            <div class="success">🔧 预测数据测试结果</div>
                            <div class="stats">
                                <div class="stat-card">
                                    <div class="stat-number">${{data.total_predictions}}</div>
                                    <div class="stat-label">总预测记录</div>
                                </div>
                            </div>
                            <h3>最近5条预测记录:</h3>
                        `;

                        if (data.recent_predictions && data.recent_predictions.length > 0) {{
                            data.recent_predictions.forEach(pred => {{
                                html += `
                                    <div class="match-card">
                                        <div>ID: ${{pred.id}} | 比赛ID: ${{pred.match_id}} | 类别: ${{pred.category}}</div>
                                        <div>预测: ${{pred.prediction_description}}</div>
                                        <div>置信度: ${{pred.confidence_level}} | 创建时间: ${{pred.created_at}}</div>
                                    </div>
                                `;
                            }});
                        }} else {{
                            html += '<div class="error">没有预测记录</div>';
                        }}

                        resultsDiv.innerHTML = html;
                    }} else {{
                        resultsDiv.innerHTML = `<div class="error">❌ 测试失败: ${{data.error}}</div>`;
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 测试失败: ${{error.message}}</div>`;
                }});
        }}

        function debugPredictions() {{
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">🐛 正在调试数据...</div>';

            fetch('/api/debug-predictions')
                .then(response => response.json())
                .then(data => {{
                    console.log('调试数据:', data);

                    if (data.success) {{
                        let html = `
                            <div class="success">🐛 数据库调试结果</div>
                            <div class="stats">
                                <div class="stat-card">
                                    <div class="stat-number">${{data.total_predictions}}</div>
                                    <div class="stat-label">总预测记录</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${{data.overall_stats.verified}}</div>
                                    <div class="stat-label">已验证记录</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${{data.overall_stats.correct}}</div>
                                    <div class="stat-label">正确预测</div>
                                </div>
                            </div>
                            <h3>最近10条预测记录:</h3>
                        `;

                        if (data.recent_predictions && data.recent_predictions.length > 0) {{
                            data.recent_predictions.forEach(pred => {{
                                const statusText = pred.is_correct === null ? '⏳ 待验证' :
                                                 (pred.is_correct ? '✅ 正确' : '❌ 错误');
                                html += `
                                    <div class="match-card">
                                        <div><strong>ID:</strong> ${{pred.id}} | <strong>比赛:</strong> ${{pred.home_team_name}} vs ${{pred.away_team_name}}</div>
                                        <div><strong>类别:</strong> ${{pred.category}} | <strong>预测:</strong> ${{pred.predicted_result}} | <strong>实际:</strong> ${{pred.actual_result || 'N/A'}}</div>
                                        <div><strong>状态:</strong> ${{pred.status}} | <strong>验证:</strong> ${{statusText}}</div>
                                        <div><strong>验证时间:</strong> ${{pred.verified_at || 'N/A'}}</div>
                                    </div>
                                `;
                            }});
                        }} else {{
                            html += '<div class="error">没有预测记录</div>';
                        }}

                        resultsDiv.innerHTML = html;
                    }} else {{
                        resultsDiv.innerHTML = `<div class="error">❌ 调试失败: ${{data.error}}</div>`;
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 调试失败: ${{error.message}}</div>`;
                }});
        }}

        function loadPredictedMatches() {{
            const params = getDateTimeParams();
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">🔍 正在加载预测记录...</div>';

            const url = `/api/predicted-matches?start_datetime=${{encodeURIComponent(params.start_datetime)}}&end_datetime=${{encodeURIComponent(params.end_datetime)}}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {{
                    console.log('🔍 加载预测比赛 - API响应数据:', data); // 调试信息
                    console.log('data.success:', data.success);
                    console.log('data.matches:', data.matches);
                    console.log('data.predictions:', data.predictions);
                    console.log('data.count:', data.count);

                    if (data.success) {{
                        // 兼容两种数据结构
                        const matches = data.matches || data.predictions || [];
                        const count = data.count || matches.length;

                        console.log('处理后的matches:', matches);
                        console.log('matches.length:', matches.length);

                        if (matches.length > 0) {{
                            console.log('调用displayPredictedMatches');
                            displayPredictedMatches(matches, count);
                        }} else {{
                            console.log('没有找到数据');
                            resultsDiv.innerHTML = '<div class="error">❌ 没有找到预测记录 (数据为空)</div>';
                        }}
                    }} else {{
                        console.log('API返回失败:', data.error);
                        resultsDiv.innerHTML = `<div class="error">❌ API错误: ${{data.error || '未知错误'}}</div>`;
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 加载失败: ${{error.message}}</div>`;
                }});
        }}

        function predictMatches() {{
            const params = getDateTimeParams();
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">🔮 正在进行AI预测...</div>';

            const url = `/api/predict?start_datetime=${{encodeURIComponent(params.start_datetime)}}&end_datetime=${{encodeURIComponent(params.end_datetime)}}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {{
                    if (data.success && data.predictions && data.predictions.length > 0) {{
                        displayPredictions(data.predictions, data.start_datetime, data.end_datetime);
                    }} else {{
                        resultsDiv.innerHTML = '<div class="error">❌ 没有预测结果</div>';
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 预测失败: ${{error.message}}</div>`;
                }});
        }}

        function verifyPredictions() {{
            const params = getDateTimeParams();
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">🔍 正在验证预测结果...</div>';

            const url = `/api/verify?start_datetime=${{encodeURIComponent(params.start_datetime)}}&end_datetime=${{encodeURIComponent(params.end_datetime)}}`;

            fetch(url)
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        const accuracy = (data.accuracy * 100).toFixed(1);
                        resultsDiv.innerHTML = `
                            <div class="success">
                                ✅ 验证完成: ${{data.verified_count}} 个预测
                                <br>正确预测: ${{data.correct_count}} 个
                                <br>准确率: ${{accuracy}}%
                                <br>${{data.message}}
                            </div>
                        `;
                    }} else {{
                        resultsDiv.innerHTML = `<div class="error">❌ 验证失败: ${{data.error}}</div>`;
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 验证失败: ${{error.message}}</div>`;
                }});
        }}

        function showStats() {{
            const resultsDiv = document.getElementById('results');

            resultsDiv.innerHTML = '<div class="loading">📊 正在加载统计数据...</div>';

            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {{
                    if (data.success && data.stats) {{
                        displayStats(data.stats);
                    }} else {{
                        resultsDiv.innerHTML = '<div class="error">❌ 没有统计数据</div>';
                    }}
                }})
                .catch(error => {{
                    resultsDiv.innerHTML = `<div class="error">❌ 加载统计失败: ${{error.message}}</div>`;
                }});
        }}

        function displayStats(stats) {{
            const resultsDiv = document.getElementById('results');

            if (!stats || stats.length === 0) {{
                resultsDiv.innerHTML = '<div class="error">❌ 暂无统计数据</div>';
                return;
            }}

            let html = `
                <div class="success">📊 预测统计报告</div>
                <div class="stats">
            `;

            // 按类别分组统计
            const categoryStats = {{}};
            stats.forEach(stat => {{
                if (!categoryStats[stat.category]) {{
                    categoryStats[stat.category] = [];
                }}
                categoryStats[stat.category].push(stat);
            }});

            const categoryNames = {{
                'goals': '⚽ 进球大小球',
                'match_result': '🏆 胜平负',
                'asian_handicap': '⚖️ 让球(亚盘)',
                'corners': '🚩 角球大小球'
            }};

            Object.entries(categoryStats).forEach(([category, categoryData]) => {{
                const totalPredictions = categoryData.reduce((sum, item) => sum + item.total_predictions, 0);
                const totalCorrect = categoryData.reduce((sum, item) => sum + item.correct_predictions, 0);
                const overallAccuracy = totalCorrect / totalPredictions * 100;

                html += `
                    <div class="stat-card">
                        <div class="stat-number">${{overallAccuracy.toFixed(1)}}%</div>
                        <div class="stat-label">${{categoryNames[category] || category}}</div>
                        <div style="font-size: 0.8em; margin-top: 5px;">
                            ${{totalCorrect}}/${{totalPredictions}} 预测
                        </div>
                    </div>
                `;
            }});

            html += '</div>';

            // 详细统计表格
            html += `
                <div style="margin-top: 30px;">
                    <h3>详细统计</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 10px; border: 1px solid #ddd;">类别</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">置信度</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">总预测</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">正确预测</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">准确率</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            stats.forEach(stat => {{
                const accuracy = (stat.accuracy_rate * 100).toFixed(1);
                const categoryName = categoryNames[stat.category] || stat.category;

                html += `
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">${{categoryName}}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${{stat.confidence_level}}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${{stat.total_predictions}}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${{stat.correct_predictions}}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${{accuracy}}%</td>
                    </tr>
                `;
            }});

            html += '</tbody></table></div>';

            resultsDiv.innerHTML = html;
        }}

        function adaptPredictionData(rawMatches) {{
            // 适配不同的数据结构
            return rawMatches.map(match => {{
                // 如果是预测API的数据结构
                if (match.match_info && match.predictions) {{
                    const adaptedMatch = {{
                        match_info: match.match_info,
                        predictions: {{}},
                        summary: {{
                            total_predictions: 0,
                            correct_predictions: 0,
                            verified_predictions: 0
                        }}
                    }};

                    // 转换predictions结构
                    Object.entries(match.predictions).forEach(([category, pred]) => {{
                        if (pred.success) {{
                            adaptedMatch.predictions[category] = {{
                                prediction_id: null,
                                handicap_line: pred.handicap_line,
                                predicted_result: pred.prediction,
                                predicted_probability: pred.probability,
                                confidence_level: pred.confidence,
                                description: pred.description,
                                actual_result: null,
                                is_correct: null,
                                verified_at: null,
                                prediction_time: new Date().toISOString()
                            }};
                            adaptedMatch.summary.total_predictions++;
                        }}
                    }});

                    return adaptedMatch;
                }}

                // 如果已经是正确的数据库记录结构
                return match;
            }});
        }}

        function displayPredictedMatches(matches, count) {{
            const resultsDiv = document.getElementById('results');

            // 适配数据结构
            const adaptedMatches = adaptPredictionData(matches);

            let totalPredictions = 0;
            let totalCorrect = 0;
            let totalVerified = 0;

            adaptedMatches.forEach(match => {{
                // 安全访问summary字段
                const summary = match.summary || {{}};
                totalPredictions += summary.total_predictions || 0;
                totalCorrect += summary.correct_predictions || 0;
                totalVerified += summary.verified_predictions || 0;
            }});

            const overallAccuracy = totalVerified > 0 ? (totalCorrect / totalVerified * 100).toFixed(1) : 'N/A';

            let html = `
                <div class="success">🔍 找到 ${{count}} 场已预测比赛</div>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${{count}}</div>
                        <div class="stat-label">预测比赛</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${{totalPredictions}}</div>
                        <div class="stat-label">总预测数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${{totalVerified}}</div>
                        <div class="stat-label">已验证</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${{overallAccuracy}}%</div>
                        <div class="stat-label">总准确率</div>
                    </div>
                </div>
            `;

            adaptedMatches.forEach((match, index) => {{
                const matchInfo = match.match_info || {{}};
                const summary = match.summary || {{total_predictions: 0, correct_predictions: 0, verified_predictions: 0}};
                const accuracy = summary.verified_predictions > 0 ?
                    (summary.correct_predictions / summary.verified_predictions * 100).toFixed(1) : 'N/A';

                html += `
                    <div class="match-card">
                        <div class="match-header">
                            <div class="match-teams">${{matchInfo.home_team || 'Unknown'}} vs ${{matchInfo.away_team || 'Unknown'}}</div>
                            <div class="match-info">
                                ${{matchInfo.league_name || 'Unknown League'}} | ${{formatDateTime(matchInfo.match_datetime)}} | ${{matchInfo.status || 'Unknown'}}
                            </div>
                        </div>
                `;

                // 显示比赛结果（如果已完成）
                if (matchInfo.status === 'FINISHED' && matchInfo.home_goals !== null) {{
                    html += `
                        <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>⚽ 比赛结果:</strong> ${{matchInfo.home_team}} ${{matchInfo.home_goals}} - ${{matchInfo.away_goals}} ${{matchInfo.away_team}}
                            | 总进球: ${{matchInfo.total_goals || 'N/A'}} | 总角球: ${{matchInfo.total_corners || 'N/A'}}
                        </div>
                    `;
                }}

                html += `
                        <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                            <strong>📊 预测统计:</strong>
                            总预测 ${{summary.total_predictions}} 个 |
                            已验证 ${{summary.verified_predictions}} 个 |
                            准确率 ${{accuracy}}%
                        </div>
                        <div class="predictions-grid">
                `;

                const categoryIcons = {{
                    'goals': '⚽',
                    'match_result': '🏆',
                    'asian_handicap': '⚖️',
                    'corners': '🚩'
                }};

                const categoryNames = {{
                    'goals': '进球大小球',
                    'match_result': '胜平负',
                    'asian_handicap': '让球(亚盘)',
                    'corners': '角球大小球'
                }};

                Object.entries(match.predictions || {{}}).forEach(([category, pred]) => {{
                    const icon = categoryIcons[category] || '📊';
                    const name = categoryNames[category] || category;

                    let statusClass = 'prediction-medium';
                    let statusText = '⏳ 待验证';

                    if (pred && pred.is_correct !== null && pred.is_correct !== undefined) {{
                        statusClass = pred.is_correct ? 'prediction-high' : 'prediction-low';
                        statusText = pred.is_correct ? '✅ 预测正确' : '❌ 预测错误';
                    }}

                    // 生成详细的预测信息
                    let predictionDetails = '';
                    if (pred.handicap_line !== null && pred.handicap_line !== undefined) {{
                        predictionDetails += `盘口: ${{pred.handicap_line}} | `;
                    }}
                    predictionDetails += `预测: ${{pred.predicted_result}} | `;
                    if (pred.actual_result !== null && pred.actual_result !== undefined) {{
                        predictionDetails += `实际: ${{pred.actual_result}}`;
                    }} else {{
                        predictionDetails += `实际: 待确定`;
                    }}

                    html += `
                        <div class="prediction ${{statusClass}}">
                            <div class="prediction-title">
                                ${{icon}} ${{name}}
                                <span style="font-size: 0.8em; margin-left: 10px; opacity: 0.8;">
                                    ID: ${{pred.prediction_id || 'N/A'}}
                                </span>
                            </div>
                            <div class="prediction-desc">${{pred.description || 'N/A'}}</div>
                            <div style="font-size: 0.9em; margin: 5px 0; color: #666;">
                                ${{predictionDetails}}
                            </div>
                            <div class="prediction-confidence">
                                置信度: ${{pred.confidence_level || 'N/A'}} (${{pred.predicted_probability ? (pred.predicted_probability * 100).toFixed(1) : 'N/A'}}%)
                            </div>
                            <div style="margin-top: 5px; font-size: 0.9em;">
                                ${{statusText}}
                                ${{pred.verified_at ? ' | 验证时间: ' + formatDateTime(pred.verified_at) : ''}}
                            </div>
                            <div style="margin-top: 3px; font-size: 0.8em; color: #888;">
                                预测时间: ${{formatDateTime(pred.prediction_time)}}
                            </div>
                        </div>
                    `;
                }});

                html += '</div></div>';
            }});

            resultsDiv.innerHTML = html;
        }}

        function displayMatches(matches, startDateTime, endDateTime) {{
            const resultsDiv = document.getElementById('results');

            let html = `
                <div class="success">✅ 找到 ${{matches.length}} 场比赛 (${{startDateTime}} 至 ${{endDateTime}})</div>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${{matches.length}}</div>
                        <div class="stat-label">待预测比赛</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">30</div>
                        <div class="stat-label">AI预测模型</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">预测类别</div>
                    </div>
                </div>
            `;
            
            matches.forEach((match, index) => {{
                html += `
                    <div class="match-card">
                        <div class="match-header">
                            <div class="match-teams">${{match.home_team_name}} vs ${{match.away_team_name}}</div>
                            <div class="match-info">
                                ${{match.league_name}} | ${{formatDateTime(match.match_datetime)}} | ${{match.status}}
                            </div>
                        </div>
                        <div style="color: #6c757d;">
                            🎯 进球盘口: ${{match.goals_line_1 || 'N/A'}} | 
                            ⚖️ 亚盘: ${{match.asian_handicap_line_1 || 'N/A'}} | 
                            🚩 角球盘口: ${{match.corners_line || 'N/A'}}
                        </div>
                    </div>
                `;
            }});
            
            resultsDiv.innerHTML = html;
        }}
        
        function displayPredictions(predictions, startDateTime, endDateTime) {{
            const resultsDiv = document.getElementById('results');

            let totalPredictions = 0;
            let highConfidence = 0;

            predictions.forEach(pred => {{
                Object.values(pred.predictions || {{}}).forEach(p => {{
                    if (p.success) {{
                        totalPredictions++;
                        if (p.confidence === 'HIGH') highConfidence++;
                    }}
                }});
            }});

            let html = `
                <div class="success">🎯 完成 ${{predictions.length}} 场比赛AI预测 (${{startDateTime}} 至 ${{endDateTime}})</div>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${{predictions.length}}</div>
                        <div class="stat-label">预测比赛</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${{totalPredictions}}</div>
                        <div class="stat-label">总预测数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${{highConfidence}}</div>
                        <div class="stat-label">高置信度</div>
                    </div>
                </div>
            `;
            
            predictions.forEach((pred, index) => {{
                const match = pred.match_info;
                
                html += `
                    <div class="match-card">
                        <div class="match-header">
                            <div class="match-teams">${{match.home_team}} vs ${{match.away_team}}</div>
                            <div class="match-info">
                                ${{match.league_name}} | ${{formatDateTime(match.match_datetime)}}
                            </div>
                        </div>
                        <div class="predictions-grid">
                `;
                
                const categoryIcons = {{
                    'goals': '⚽',
                    'match_result': '🏆',
                    'asian_handicap': '⚖️',
                    'corners': '🚩'
                }};
                
                const categoryNames = {{
                    'goals': '进球大小球',
                    'match_result': '胜平负',
                    'asian_handicap': '让球(亚盘)',
                    'corners': '角球大小球'
                }};
                
                Object.entries(pred.predictions || {{}}).forEach(([category, result]) => {{
                    if (result.success) {{
                        const confidenceClass = `prediction-${{result.confidence.toLowerCase()}}`;
                        const icon = categoryIcons[category] || '📊';
                        const name = categoryNames[category] || category;
                        
                        html += `
                            <div class="prediction ${{confidenceClass}}">
                                <div class="prediction-title">
                                    ${{icon}} ${{name}}
                                </div>
                                <div class="prediction-desc">${{result.description}}</div>
                                <div class="prediction-confidence">置信度: ${{result.confidence}} (${{(result.probability * 100).toFixed(1)}}%)</div>
                            </div>
                        `;
                    }}
                }});
                
                html += '</div></div>';
            }});
            
            resultsDiv.innerHTML = html;
        }}
    </script>
</body>
</html>
        """

def start_web_server(port=8080):
    """启动Web服务器"""
    try:
        with socketserver.TCPServer(("", port), FootballPredictionHandler) as httpd:
            logger.info(f"🌐 足球预测Web服务器启动成功!")
            logger.info(f"📱 访问地址: http://localhost:{port}")
            logger.info(f"🎯 功能: 实时预测、比赛查询、AI分析")
            logger.info(f"⚽ 模型: 30个专用模型已加载")
            logger.info("按 Ctrl+C 停止服务器")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        logger.info("\n🛑 服务器已停止")
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    start_web_server(8082)
