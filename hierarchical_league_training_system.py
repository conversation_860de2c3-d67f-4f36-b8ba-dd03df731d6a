#!/usr/bin/env python3
"""
分层联赛训练系统
为五大联赛、一级赛事、杯赛的具体赛事训练专用模型
基于近3年历史数据
"""

import pandas as pd
import numpy as np
import logging
import os
import pymysql
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hierarchical_training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("HierarchicalTraining")

class HierarchicalLeagueTrainingSystem:
    """分层联赛训练系统"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.models = {}
        self.league_hierarchy = {}
        self.training_results = {}
        
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.db_config)
    
    def analyze_league_hierarchy(self) -> Dict:
        """分析联赛层级和数据量"""
        logger.info("分析联赛层级和近3年数据量...")
        
        with self.get_connection() as conn:
            query = """
            SELECT 
                l.id as league_id,
                l.name as league_name,
                l.english_name,
                l.priority,
                l.country,
                CASE 
                    WHEN l.priority = 0 THEN '五大联赛'
                    WHEN l.priority = 1 THEN '一级赛事'
                    WHEN l.priority = 99 THEN '杯赛'
                    ELSE '其他联赛'
                END as tier_name,
                
                -- 近3年数据统计
                COUNT(DISTINCT m.id) as total_matches,
                COUNT(DISTINCT CASE WHEN ms.total_goals IS NOT NULL THEN m.id END) as matches_with_stats,
                COUNT(DISTINCT CASE WHEN o.match_id IS NOT NULL THEN m.id END) as matches_with_odds,
                
                -- 盘口线覆盖
                COUNT(DISTINCT o.goals_line_1) as unique_goals_lines,
                COUNT(DISTINCT o.asian_handicap_line_1) as unique_asian_lines,
                COUNT(DISTINCT o.corners_line) as unique_corners_lines,
                
                -- 数据质量
                AVG(CASE WHEN ms.total_goals IS NOT NULL THEN ms.total_goals END) as avg_goals,
                AVG(CASE WHEN ms.total_corners IS NOT NULL THEN ms.total_corners END) as avg_corners,
                
                -- 时间覆盖
                MIN(m.match_date) as earliest_date,
                MAX(m.match_date) as latest_date,
                COUNT(DISTINCT YEAR(m.match_date)) as years_covered
                
            FROM leagues l
            JOIN matches m ON l.id = m.league_id
            LEFT JOIN match_stats ms ON m.id = ms.match_id
            LEFT JOIN odds o ON m.id = o.match_id 
                              AND o.bookmaker = '365bet' 
                              AND o.odds_type = 'OPENING'
            
            WHERE m.match_date >= DATE_SUB(NOW(), INTERVAL 3 YEAR)
            GROUP BY l.id, l.name, l.english_name, l.priority, l.country
            HAVING total_matches >= 50  -- 至少50场比赛
            ORDER BY l.priority, total_matches DESC
            """
            
            df = pd.read_sql(query, conn)
        
        # 按层级组织数据
        hierarchy = {
            '五大联赛': {},
            '一级赛事': {},
            '杯赛': {},
            '其他联赛': {}
        }
        
        for _, row in df.iterrows():
            tier = row['tier_name']
            league_info = {
                'league_id': row['league_id'],
                'league_name': row['league_name'],
                'english_name': row['english_name'],
                'country': row['country'],
                'total_matches': row['total_matches'],
                'matches_with_stats': row['matches_with_stats'],
                'matches_with_odds': row['matches_with_odds'],
                'data_quality': row['matches_with_stats'] / row['total_matches'] if row['total_matches'] > 0 else 0,
                'odds_coverage': row['matches_with_odds'] / row['total_matches'] if row['total_matches'] > 0 else 0,
                'unique_goals_lines': row['unique_goals_lines'],
                'unique_asian_lines': row['unique_asian_lines'],
                'avg_goals': row['avg_goals'],
                'years_covered': row['years_covered']
            }
            
            hierarchy[tier][row['league_name']] = league_info
        
        self.league_hierarchy = hierarchy
        
        # 显示分析结果
        self._display_hierarchy_analysis()
        
        return hierarchy
    
    def _display_hierarchy_analysis(self):
        """显示层级分析结果"""
        logger.info("\n" + "="*100)
        logger.info("联赛层级分析结果 (近3年数据)")
        logger.info("="*100)
        
        for tier_name, leagues in self.league_hierarchy.items():
            if not leagues:
                continue
                
            logger.info(f"\n🏆 {tier_name} ({len(leagues)} 个联赛):")
            logger.info("联赛名称 | 比赛数 | 数据质量 | 赔率覆盖 | 盘口线数 | 平均进球 | 年份")
            logger.info("-" * 90)
            
            # 按比赛数排序
            sorted_leagues = sorted(leagues.items(), key=lambda x: x[1]['total_matches'], reverse=True)
            
            for league_name, info in sorted_leagues[:15]:  # 显示前15个
                logger.info(f"{league_name[:15]:>15} | {info['total_matches']:>6} | "
                          f"{info['data_quality']:>7.1%} | {info['odds_coverage']:>7.1%} | "
                          f"{info['unique_goals_lines']:>8} | {info['avg_goals']:>7.2f} | "
                          f"{info['years_covered']:>4}")
    
    def determine_training_strategy(self) -> Dict:
        """确定训练策略 - 为每个重要联赛都训练专用模型"""
        logger.info("\n确定各联赛的训练策略...")

        strategies = {
            'specific_models': [],      # 独立训练的联赛
            'grouped_models': {},       # 分组训练的联赛 (很少使用)
            'fallback_models': []       # 使用通用模型的联赛
        }

        # 定义最低数据要求
        min_requirements = {
            '五大联赛': {'min_matches': 100, 'min_quality': 0.7},
            '一级赛事': {'min_matches': 100, 'min_quality': 0.7},
            '杯赛': {'min_matches': 50, 'min_quality': 0.7},
            '其他联赛': {'min_matches': 200, 'min_quality': 0.8}
        }

        for tier_name, leagues in self.league_hierarchy.items():
            requirements = min_requirements.get(tier_name, {'min_matches': 200, 'min_quality': 0.8})

            for league_name, info in leagues.items():
                total_matches = info['total_matches']
                data_quality = info['data_quality']
                odds_coverage = info['odds_coverage']

                # 新策略：五大联赛、一级赛事、杯赛都尽量独立训练
                if tier_name in ['五大联赛', '一级赛事', '杯赛']:
                    if (total_matches >= requirements['min_matches'] and
                        data_quality >= requirements['min_quality']):
                        strategies['specific_models'].append({
                            'league_id': info['league_id'],
                            'league_name': league_name,
                            'tier': tier_name,
                            'strategy': 'SPECIFIC',
                            'reason': f'{tier_name}专用模型，数据量{total_matches}场',
                            'total_matches': total_matches,
                            'data_quality': data_quality
                        })
                    else:
                        # 数据不足的重要联赛，降低要求也要训练
                        if total_matches >= 30 and data_quality >= 0.5:
                            strategies['specific_models'].append({
                                'league_id': info['league_id'],
                                'league_name': league_name,
                                'tier': tier_name,
                                'strategy': 'SPECIFIC_LOW_DATA',
                                'reason': f'{tier_name}专用模型(低数据量)，数据量{total_matches}场',
                                'total_matches': total_matches,
                                'data_quality': data_quality
                            })
                        else:
                            strategies['fallback_models'].append({
                                'league_id': info['league_id'],
                                'league_name': league_name,
                                'tier': tier_name,
                                'strategy': 'FALLBACK',
                                'reason': f'数据严重不足({total_matches}场)，使用通用模型',
                                'total_matches': total_matches,
                                'data_quality': data_quality
                            })

                else:
                    # 其他联赛：数据量足够的也独立训练
                    if (total_matches >= requirements['min_matches'] and
                        data_quality >= requirements['min_quality']):
                        strategies['specific_models'].append({
                            'league_id': info['league_id'],
                            'league_name': league_name,
                            'tier': tier_name,
                            'strategy': 'SPECIFIC',
                            'reason': f'其他联赛专用模型，数据量{total_matches}场',
                            'total_matches': total_matches,
                            'data_quality': data_quality
                        })
                    else:
                        strategies['fallback_models'].append({
                            'league_id': info['league_id'],
                            'league_name': league_name,
                            'tier': tier_name,
                            'strategy': 'FALLBACK',
                            'reason': f'数据不足({total_matches}场)，使用通用模型',
                            'total_matches': total_matches,
                            'data_quality': data_quality
                        })

        self._display_training_strategy(strategies)
        return strategies
    
    def _display_training_strategy(self, strategies: Dict):
        """显示训练策略"""
        logger.info("\n" + "="*100)
        logger.info("全面专用模型训练策略")
        logger.info("="*100)

        # 按层级分组显示独立训练模型
        specific_by_tier = {}
        for item in strategies['specific_models']:
            tier = item['tier']
            if tier not in specific_by_tier:
                specific_by_tier[tier] = []
            specific_by_tier[tier].append(item)

        logger.info(f"\n🎯 独立训练模型 (总计 {len(strategies['specific_models'])} 个联赛):")

        for tier_name in ['五大联赛', '一级赛事', '杯赛', '其他联赛']:
            if tier_name in specific_by_tier:
                tier_leagues = specific_by_tier[tier_name]
                logger.info(f"\n  🏆 {tier_name} ({len(tier_leagues)} 个联赛):")

                # 按数据量排序
                tier_leagues.sort(key=lambda x: x['total_matches'], reverse=True)

                for item in tier_leagues:
                    status_icon = "✅" if item['strategy'] == 'SPECIFIC' else "⚠️"
                    logger.info(f"    {status_icon} {item['league_name']:<25} | "
                              f"{item['total_matches']:>4}场 | "
                              f"质量{item['data_quality']:>5.1%} | "
                              f"{item['reason']}")

        # 显示分组训练（如果有）
        if strategies['grouped_models']:
            logger.info(f"\n🔗 分组训练模型 ({len(strategies['grouped_models'])} 个组):")
            for group_name, group_leagues in strategies['grouped_models'].items():
                logger.info(f"  📊 {group_name} ({len(group_leagues)} 个联赛):")
                for item in group_leagues:
                    logger.info(f"    - {item['league_name']}")

        # 显示使用通用模型的联赛
        if strategies['fallback_models']:
            logger.info(f"\n🔄 使用通用模型 ({len(strategies['fallback_models'])} 个联赛):")

            # 按层级分组显示
            fallback_by_tier = {}
            for item in strategies['fallback_models']:
                tier = item['tier']
                if tier not in fallback_by_tier:
                    fallback_by_tier[tier] = []
                fallback_by_tier[tier].append(item)

            for tier_name, tier_leagues in fallback_by_tier.items():
                logger.info(f"  📂 {tier_name} ({len(tier_leagues)} 个联赛):")
                for item in tier_leagues[:5]:  # 每个层级只显示前5个
                    logger.info(f"    ⚪ {item['league_name']:<25} | "
                              f"{item['total_matches']:>4}场 | "
                              f"质量{item['data_quality']:>5.1%}")
                if len(tier_leagues) > 5:
                    logger.info(f"    ... 还有 {len(tier_leagues) - 5} 个联赛")

        # 显示统计总结
        logger.info(f"\n📊 训练策略统计:")
        logger.info(f"  🎯 独立训练: {len(strategies['specific_models'])} 个联赛")
        logger.info(f"  🔗 分组训练: {len(strategies['grouped_models'])} 个组")
        logger.info(f"  🔄 通用模型: {len(strategies['fallback_models'])} 个联赛")

        total_leagues = len(strategies['specific_models']) + len(strategies['fallback_models'])
        if total_leagues > 0:
            specific_rate = len(strategies['specific_models']) / total_leagues * 100
            logger.info(f"  📈 专用模型覆盖率: {specific_rate:.1f}%")
    
    def get_league_training_data(self, league_ids: List[int], prediction_type: str = 'goals') -> pd.DataFrame:
        """获取特定联赛的训练数据"""
        
        if not league_ids:
            return pd.DataFrame()
        
        league_ids_str = ','.join(map(str, league_ids))
        
        with self.get_connection() as conn:
            query = f"""
            SELECT 
                m.id as match_id,
                m.league_id,
                l.name as league_name,
                l.priority as league_priority,
                m.home_team_name,
                m.away_team_name,
                m.match_date,
                
                -- 比赛统计
                ms.home_goals, ms.away_goals, ms.total_goals,
                ms.home_corners, ms.away_corners, ms.total_corners,
                ms.home_yellow_cards, ms.away_yellow_cards, ms.total_yellow_cards,
                ms.home_shots, ms.away_shots, ms.total_shots,
                ms.home_shots_on_target, ms.away_shots_on_target,
                ms.home_possession, ms.away_possession,
                ms.home_attacks, ms.away_attacks, ms.total_attacks,
                ms.home_dangerous_attacks, ms.away_dangerous_attacks,
                
                -- 初盘赔率
                o1.goals_line_1 as opening_goals_line,
                o1.goals_over_odds_1 as opening_goals_over_odds,
                o1.goals_under_odds_1 as opening_goals_under_odds,
                o1.asian_handicap_line_1 as opening_asian_line,
                o1.asian_home_odds_1 as opening_asian_home_odds,
                o1.asian_away_odds_1 as opening_asian_away_odds,
                o1.home_win_odds as opening_home_odds,
                o1.draw_odds as opening_draw_odds,
                o1.away_win_odds as opening_away_odds,
                
                -- 即时盘赔率
                o2.goals_line_1 as live_goals_line,
                o2.goals_over_odds_1 as live_goals_over_odds,
                o2.goals_under_odds_1 as live_goals_under_odds,
                o2.asian_handicap_line_1 as live_asian_line,
                o2.asian_home_odds_1 as live_asian_home_odds,
                o2.asian_away_odds_1 as live_asian_away_odds,
                o2.home_win_odds as live_home_odds,
                o2.draw_odds as live_draw_odds,
                o2.away_win_odds as live_away_odds
                
            FROM matches m
            JOIN leagues l ON m.league_id = l.id
            JOIN match_stats ms ON m.id = ms.match_id
            LEFT JOIN odds o1 ON m.id = o1.match_id 
                              AND o1.odds_type = 'OPENING' 
                              AND o1.bookmaker = '365bet'
            LEFT JOIN odds o2 ON m.id = o2.match_id 
                              AND o2.odds_type = 'LIVE' 
                              AND o2.bookmaker = '365bet'
            
            WHERE m.league_id IN ({league_ids_str})
            AND m.match_date >= DATE_SUB(NOW(), INTERVAL 3 YEAR)
            AND ms.total_goals IS NOT NULL
            
            ORDER BY m.match_date DESC
            """
            
            df = pd.read_sql(query, conn)
        
        logger.info(f"获取到联赛训练数据: {len(df)} 场比赛")
        return df
    
    def create_enhanced_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建增强特征"""
        df = data.copy()
        
        # 1. 赔率变化特征
        df['goals_line_change'] = df['live_goals_line'] - df['opening_goals_line']
        df['goals_over_odds_change'] = df['live_goals_over_odds'] - df['opening_goals_over_odds']
        df['goals_under_odds_change'] = df['live_goals_under_odds'] - df['opening_goals_under_odds']
        
        df['asian_line_change'] = df['live_asian_line'] - df['opening_asian_line']
        df['asian_home_odds_change'] = df['live_asian_home_odds'] - df['opening_asian_home_odds']
        df['asian_away_odds_change'] = df['live_asian_away_odds'] - df['opening_asian_away_odds']
        
        # 2. 市场情绪特征
        df['goals_market_sentiment'] = df['goals_line_change'].apply(
            lambda x: 1 if x > 0.1 else (-1 if x < -0.1 else 0)
        )
        
        df['asian_market_sentiment'] = df['asian_line_change'].apply(
            lambda x: 1 if x > 0.1 else (-1 if x < -0.1 else 0)
        )
        
        # 3. 基础概率特征
        if 'opening_home_odds' in df.columns:
            df['home_win_prob'] = 1 / df['opening_home_odds'].fillna(3)
            df['draw_prob'] = 1 / df['opening_draw_odds'].fillna(3)
            df['away_win_prob'] = 1 / df['opening_away_odds'].fillna(3)
            
            total_prob = df['home_win_prob'] + df['draw_prob'] + df['away_win_prob']
            df['home_win_prob_norm'] = df['home_win_prob'] / total_prob
            df['draw_prob_norm'] = df['draw_prob'] / total_prob
            df['away_win_prob_norm'] = df['away_win_prob'] / total_prob
        
        # 4. 比赛统计特征
        if 'home_shots' in df.columns and 'away_shots' in df.columns:
            df['shots_diff'] = df['home_shots'] - df['away_shots']
            df['shots_ratio'] = df['home_shots'] / (df['away_shots'] + 1)
        
        if 'home_attacks' in df.columns and 'away_attacks' in df.columns:
            df['attacks_diff'] = df['home_attacks'] - df['away_attacks']
            df['attacks_ratio'] = df['home_attacks'] / (df['away_attacks'] + 1)
        
        if 'home_possession' in df.columns and 'away_possession' in df.columns:
            df['possession_diff'] = df['home_possession'] - df['away_possession']
        
        # 5. 时间特征
        df['match_date'] = pd.to_datetime(df['match_date'])
        df['month'] = df['match_date'].dt.month
        df['day_of_week'] = df['match_date'].dt.dayofweek
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        
        # 6. 联赛特征
        df['is_top_league'] = (df['league_priority'] == 0).astype(int)
        df['is_tier1_league'] = (df['league_priority'] == 1).astype(int)
        df['is_cup'] = (df['league_priority'] == 99).astype(int)
        
        # 填充缺失值
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].fillna(0)
        
        return df

    def train_league_specific_model(self, league_info: Dict, handicap_lines: List[float] = None) -> Dict:
        """为特定联赛训练模型"""

        if handicap_lines is None:
            handicap_lines = [2.5, 3.5]  # 默认训练这两个盘口

        league_id = league_info['league_id']
        league_name = league_info['league_name']
        tier = league_info['tier']

        logger.info(f"训练 {league_name} ({tier}) 专用模型...")

        # 获取训练数据
        training_data = self.get_league_training_data([league_id])

        if training_data.empty:
            return {'success': False, 'reason': 'no_data', 'league_name': league_name}

        # 创建特征
        enhanced_data = self.create_enhanced_features(training_data)

        results = {
            'success': True,
            'league_id': league_id,
            'league_name': league_name,
            'tier': tier,
            'total_data': len(enhanced_data),
            'models': {}
        }

        # 为每个盘口线训练模型
        for handicap_line in handicap_lines:
            # 过滤该盘口线的数据
            line_data = enhanced_data[
                abs(enhanced_data['opening_goals_line'] - handicap_line) < 0.1
            ].copy()

            if len(line_data) < 30:  # 降低最小要求
                logger.warning(f"{league_name} 盘口{handicap_line} 数据不足: {len(line_data)}场")
                continue

            try:
                # 创建目标变量
                y = (line_data['total_goals'] > handicap_line).astype(int)

                # 选择特征
                feature_columns = [
                    'goals_line_change', 'goals_market_sentiment',
                    'home_win_prob_norm', 'draw_prob_norm', 'away_win_prob_norm',
                    'shots_diff', 'attacks_diff', 'possession_diff',
                    'home_shots_on_target', 'away_shots_on_target',
                    'home_dangerous_attacks', 'away_dangerous_attacks',
                    'month', 'day_of_week', 'is_weekend'
                ]

                # 只保留存在的特征
                available_features = [f for f in feature_columns if f in line_data.columns]
                X = line_data[available_features].fillna(0)

                # 分割数据
                if len(X) >= 50:
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, y, test_size=0.2, random_state=42, stratify=y
                    )
                else:
                    # 数据量少时使用全部数据训练
                    X_train, X_test, y_train, y_test = X, X, y, y

                # 训练模型
                model = RandomForestClassifier(
                    n_estimators=50,  # 减少树的数量避免过拟合
                    max_depth=8,
                    min_samples_split=5,
                    min_samples_leaf=3,
                    random_state=42
                )

                model.fit(X_train, y_train)

                # 评估
                train_score = model.score(X_train, y_train)
                test_score = model.score(X_test, y_test) if len(X_test) > 0 else train_score

                # 交叉验证（如果数据足够）
                if len(X) >= 30:
                    cv_scores = cross_val_score(model, X, y, cv=min(5, len(X)//6))
                    cv_mean = cv_scores.mean()
                    cv_std = cv_scores.std()
                else:
                    cv_mean = test_score
                    cv_std = 0.0

                # 保存模型
                model_dir = f"saved_models/league_specific/{tier.replace(' ', '_')}"
                os.makedirs(model_dir, exist_ok=True)

                safe_league_name = league_name.replace('/', '_').replace('\\', '_').replace(':', '_')
                model_filename = f"goals_{safe_league_name}_line_{handicap_line}.joblib"
                model_path = os.path.join(model_dir, model_filename)

                model_info = {
                    'model': model,
                    'feature_columns': available_features,
                    'league_id': league_id,
                    'league_name': league_name,
                    'handicap_line': handicap_line,
                    'tier': tier
                }

                joblib.dump(model_info, model_path)

                # 记录结果
                results['models'][f'line_{handicap_line}'] = {
                    'path': model_path,
                    'data_count': len(line_data),
                    'train_score': train_score,
                    'test_score': test_score,
                    'cv_mean': cv_mean,
                    'cv_std': cv_std,
                    'feature_count': len(available_features)
                }

                logger.info(f"  ✅ {league_name} 盘口{handicap_line}: "
                          f"准确率{test_score:.3f} (CV:{cv_mean:.3f}±{cv_std:.3f}) "
                          f"数据{len(line_data)}场")

            except Exception as e:
                logger.error(f"  ❌ {league_name} 盘口{handicap_line} 训练失败: {e}")
                continue

        return results

def main():
    """主函数"""
    logger.info("启动分层联赛训练系统")
    
    try:
        system = HierarchicalLeagueTrainingSystem()
        
        # 分析联赛层级
        hierarchy = system.analyze_league_hierarchy()
        
        # 确定训练策略
        strategies = system.determine_training_strategy()

        logger.info("\n🎉 分层联赛分析完成！")
        logger.info(f"📊 发现联赛总数: {sum(len(leagues) for leagues in hierarchy.values())}")
        logger.info(f"🎯 独立训练: {len(strategies['specific_models'])} 个联赛")
        logger.info(f"🔗 分组训练: {len(strategies['grouped_models'])} 个组")
        logger.info(f"🔄 通用模型: {len(strategies['fallback_models'])} 个联赛")

        # 开始训练模型
        logger.info("\n开始训练联赛专用模型...")

        successful_leagues = 0
        total_models = 0

        # 只训练前20个最重要的联赛作为演示
        important_leagues = strategies['specific_models'][:20]

        for i, league_info in enumerate(important_leagues):
            logger.info(f"\n进度: {i+1}/{len(important_leagues)}")

            try:
                result = system.train_league_specific_model(league_info)

                if result['success']:
                    successful_leagues += 1
                    total_models += len(result['models'])

                    if result['models']:
                        # 显示最佳模型
                        best_model = max(result['models'].values(), key=lambda x: x['test_score'])
                        logger.info(f"  🏆 最佳模型: 盘口{best_model['path'].split('_line_')[-1].split('.')[0]} "
                                  f"准确率{best_model['test_score']:.3f}")
                else:
                    logger.warning(f"  ❌ {league_info['league_name']} 训练失败: {result.get('reason', 'unknown')}")

            except Exception as e:
                logger.error(f"  💥 {league_info['league_name']} 训练异常: {e}")

        # 显示最终总结
        logger.info(f"\n{'='*80}")
        logger.info("全面联赛专用模型训练完成")
        logger.info(f"{'='*80}")
        logger.info(f"📊 训练统计:")
        logger.info(f"  成功训练联赛: {successful_leagues}/{len(important_leagues)}")
        logger.info(f"  总训练模型数: {total_models}")
        logger.info(f"  成功率: {successful_leagues/len(important_leagues)*100:.1f}%")
        logger.info(f"📁 模型保存位置: saved_models/league_specific/")
        logger.info(f"🎯 每个联赛都有专用模型，支持动态盘口线预测！")
        
    except Exception as e:
        logger.error(f"系统运行失败: {e}")
        raise

if __name__ == "__main__":
    main()
