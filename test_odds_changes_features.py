#!/usr/bin/env python3
"""
测试赔率变化特征的效果
分析OPENING -> LIVE的赔率变化对预测准确性的影响
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from models.enhanced_data_processor import EnhancedFootballDataProcessor
from models.goals_model import GoalsModel

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('odds_changes_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("OddsChangesTest")

def analyze_odds_changes_patterns():
    """分析赔率变化模式"""
    logger.info("分析赔率变化模式")
    
    processor = EnhancedFootballDataProcessor(DB_CONFIG)
    
    # 获取最近3个月的五大联赛数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    # 获取比赛数据
    with processor.get_connection() as conn:
        match_query = """
        SELECT 
            m.id as match_id,
            m.league_id,
            l.name as league_name,
            l.priority as league_priority,
            m.home_team_name,
            m.away_team_name,
            m.match_date,
            ms.total_goals
        FROM matches m
        JOIN leagues l ON m.league_id = l.id
        JOIN match_stats ms ON m.id = ms.match_id
        WHERE l.priority = 0  -- 五大联赛
        AND m.match_date >= %s 
        AND m.match_date <= %s
        AND ms.total_goals IS NOT NULL
        ORDER BY m.match_date DESC
        LIMIT 500
        """
        
        match_data = pd.read_sql(match_query, conn, params=[start_date, end_date])
    
    if match_data.empty:
        logger.error("没有获取到比赛数据")
        return
    
    logger.info(f"获取到 {len(match_data)} 场比赛数据")
    
    # 获取赔率变化数据
    match_ids = match_data['match_id'].tolist()
    odds_changes_data = processor.get_odds_changes_data(match_ids)
    
    if odds_changes_data.empty:
        logger.error("没有获取到赔率数据")
        return
    
    # 计算赔率变化特征
    odds_changes_features = processor.calculate_odds_changes(odds_changes_data)
    
    if odds_changes_features.empty:
        logger.error("没有计算出赔率变化特征")
        return
    
    logger.info(f"计算了 {len(odds_changes_features)} 场比赛的赔率变化特征")
    
    # 分析赔率变化模式
    logger.info("\n" + "="*60)
    logger.info("赔率变化模式分析")
    logger.info("="*60)
    
    # 1. 盘口变化分析
    line_changes = odds_changes_features['goals_line_change'].dropna()
    if not line_changes.empty:
        logger.info(f"\n📊 进球盘口变化分析:")
        logger.info(f"   升盘比例: {(line_changes > 0).mean():.1%}")
        logger.info(f"   降盘比例: {(line_changes < 0).mean():.1%}")
        logger.info(f"   平盘比例: {(line_changes == 0).mean():.1%}")
        logger.info(f"   平均变化: {line_changes.mean():.3f}")
        logger.info(f"   最大升盘: {line_changes.max():.2f}")
        logger.info(f"   最大降盘: {line_changes.min():.2f}")
    
    # 2. 市场情绪分析
    sentiment_counts = odds_changes_features['market_sentiment_change'].value_counts()
    if not sentiment_counts.empty:
        logger.info(f"\n🎯 市场情绪变化分析:")
        for sentiment, count in sentiment_counts.items():
            pct = count / len(odds_changes_features) * 100
            logger.info(f"   {sentiment}: {count} 场 ({pct:.1f}%)")
    
    # 3. 变化幅度分析
    change_level_counts = odds_changes_features['goals_line_change_level'].value_counts()
    if not change_level_counts.empty:
        logger.info(f"\n📈 盘口变化幅度分析:")
        for level, count in change_level_counts.items():
            pct = count / len(odds_changes_features) * 100
            logger.info(f"   {level}: {count} 场 ({pct:.1f}%)")
    
    # 4. 合并比赛结果分析预测价值
    # 先检查数据结构
    logger.info(f"比赛数据列: {list(match_data.columns)}")
    logger.info(f"赔率变化特征列: {list(odds_changes_features.columns)}")

    # 选择Bet365的数据，如果没有则选择第一个博彩公司
    bet365_changes = odds_changes_features[odds_changes_features['bookmaker'] == 'Bet365']
    if bet365_changes.empty:
        logger.info("没有Bet365数据，使用第一个博彩公司")
        bet365_changes = odds_changes_features.groupby('match_id').first().reset_index()

    analysis_data = match_data.merge(bet365_changes, on='match_id', how='inner')
    logger.info(f"合并后数据: {len(analysis_data)} 场比赛")

    if not analysis_data.empty:
        logger.info(f"\n🔍 赔率变化与比赛结果关联分析 ({len(analysis_data)} 场比赛):")

        # 分析升盘/降盘与实际结果的关系
        for line_value in [2.5, 3.5]:
            # 检查是否有goals_line_1字段
            if 'goals_line_1' not in analysis_data.columns:
                logger.warning("没有goals_line_1字段，跳过盘口分析")
                break

            line_data = analysis_data[
                abs(analysis_data['goals_line_1'] - line_value) < 0.1
            ].copy()

            if len(line_data) < 10:
                continue

            logger.info(f"\n   📊 盘口 {line_value} 分析 ({len(line_data)} 场):")

            # 计算基准命中率
            baseline_over_rate = (line_data['total_goals'] > line_value).mean()
            logger.info(f"     基准大球命中率: {baseline_over_rate:.1%}")

            # 升盘情况
            up_data = line_data[line_data['goals_line_change'] > 0]
            if not up_data.empty:
                over_rate = (up_data['total_goals'] > line_value).mean()
                improvement = over_rate - baseline_over_rate
                logger.info(f"     🔺 升盘 ({len(up_data)} 场): 大球命中率 {over_rate:.1%} (vs基准 {improvement:+.1%})")

            # 降盘情况
            down_data = line_data[line_data['goals_line_change'] < 0]
            if not down_data.empty:
                over_rate = (down_data['total_goals'] > line_value).mean()
                improvement = over_rate - baseline_over_rate
                logger.info(f"     🔻 降盘 ({len(down_data)} 场): 大球命中率 {over_rate:.1%} (vs基准 {improvement:+.1%})")

            # 平盘情况
            stable_data = line_data[line_data['goals_line_change'] == 0]
            if not stable_data.empty:
                over_rate = (stable_data['total_goals'] > line_value).mean()
                improvement = over_rate - baseline_over_rate
                logger.info(f"     ➡️ 平盘 ({len(stable_data)} 场): 大球命中率 {over_rate:.1%} (vs基准 {improvement:+.1%})")

        # 5. 市场情绪与结果关联分析
        logger.info(f"\n🎯 市场情绪与结果关联分析:")

        sentiment_analysis = analysis_data.groupby('market_sentiment_change').agg({
            'total_goals': ['count', 'mean'],
            'match_id': lambda x: (analysis_data.loc[x.index, 'total_goals'] > 2.5).mean()
        }).round(3)

        for sentiment in ['FAVOR_OVER', 'NEUTRAL', 'FAVOR_UNDER']:
            sentiment_data = analysis_data[analysis_data['market_sentiment_change'] == sentiment]
            if not sentiment_data.empty:
                avg_goals = sentiment_data['total_goals'].mean()
                over_25_rate = (sentiment_data['total_goals'] > 2.5).mean()
                logger.info(f"   {sentiment} ({len(sentiment_data)} 场): 平均进球 {avg_goals:.2f}, 大2.5球率 {over_25_rate:.1%}")

        # 6. 变化幅度与结果关联
        logger.info(f"\n📈 变化幅度与结果关联:")

        for change_level in ['STABLE', 'MINOR', 'MODERATE', 'MAJOR']:
            level_data = analysis_data[analysis_data['goals_line_change_level'] == change_level]
            if not level_data.empty:
                avg_goals = level_data['total_goals'].mean()
                over_25_rate = (level_data['total_goals'] > 2.5).mean()
                logger.info(f"   {change_level} ({len(level_data)} 场): 平均进球 {avg_goals:.2f}, 大2.5球率 {over_25_rate:.1%}")

        # 7. 综合预测价值评估
        logger.info(f"\n⭐ 综合预测价值评估:")

        # 强信号组合
        strong_over_signals = analysis_data[
            (analysis_data['goals_line_change'] > 0.25) &
            (analysis_data['market_sentiment_change'] == 'FAVOR_OVER')
        ]

        strong_under_signals = analysis_data[
            (analysis_data['goals_line_change'] < -0.25) &
            (analysis_data['market_sentiment_change'] == 'FAVOR_UNDER')
        ]

        if not strong_over_signals.empty:
            over_rate = (strong_over_signals['total_goals'] > 2.5).mean()
            logger.info(f"   🔥 强大球信号 (升盘>0.25 + 市场倾向大球): {len(strong_over_signals)} 场, 命中率 {over_rate:.1%}")

        if not strong_under_signals.empty:
            under_rate = (strong_under_signals['total_goals'] <= 2.5).mean()
            logger.info(f"   🧊 强小球信号 (降盘>0.25 + 市场倾向小球): {len(strong_under_signals)} 场, 命中率 {under_rate:.1%}")

        # 计算预测价值指标
        logger.info(f"\n📊 预测价值统计:")

        # 升盘准确性
        up_games = analysis_data[analysis_data['goals_line_change'] > 0]
        if not up_games.empty:
            up_accuracy = (up_games['total_goals'] > 2.5).mean()
            logger.info(f"   升盘预测大球准确率: {up_accuracy:.1%}")

        # 降盘准确性
        down_games = analysis_data[analysis_data['goals_line_change'] < 0]
        if not down_games.empty:
            down_accuracy = (down_games['total_goals'] <= 2.5).mean()
            logger.info(f"   降盘预测小球准确率: {down_accuracy:.1%}")

        # 整体相关性
        if 'goals_line_change' in analysis_data.columns:
            correlation = analysis_data['goals_line_change'].corr(analysis_data['total_goals'])
            logger.info(f"   盘口变化与进球数相关性: {correlation:.3f}")
    
    return analysis_data

def test_enhanced_model_with_odds_changes():
    """测试包含赔率变化特征的增强模型"""
    logger.info("\n" + "="*60)
    logger.info("测试增强模型 (包含赔率变化特征)")
    logger.info("="*60)
    
    processor = EnhancedFootballDataProcessor(DB_CONFIG)
    
    # 获取训练数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    
    with processor.get_connection() as conn:
        match_query = """
        SELECT 
            m.id as match_id,
            m.league_id,
            l.name as league_name,
            l.priority as league_priority,
            m.home_team_name,
            m.away_team_name,
            m.match_date,
            ms.home_goals, ms.away_goals, ms.total_goals,
            ms.home_shots, ms.away_shots,
            ms.home_attacks, ms.away_attacks,
            ms.home_shots_on_target, ms.away_shots_on_target,
            ms.home_dangerous_attacks, ms.away_dangerous_attacks
        FROM matches m
        JOIN leagues l ON m.league_id = l.id
        JOIN match_stats ms ON m.id = ms.match_id
        WHERE l.priority = 0  -- 五大联赛
        AND m.match_date >= %s 
        AND m.match_date <= %s
        AND ms.total_goals IS NOT NULL
        ORDER BY m.match_date DESC
        LIMIT 1000
        """
        
        match_data = pd.read_sql(match_query, conn, params=[start_date, end_date])
    
    if len(match_data) < 100:
        logger.error("训练数据不足")
        return
    
    logger.info(f"获取到 {len(match_data)} 场训练数据")
    
    # 获取赔率数据
    match_ids = match_data['match_id'].tolist()
    odds_data = processor.get_odds_changes_data(match_ids)
    
    # 创建增强特征
    enhanced_features = processor.create_enhanced_features(match_data, odds_data)
    
    # 过滤有完整数据的比赛
    complete_data = enhanced_features.dropna(subset=['total_goals', 'goals_line_1'])
    
    logger.info(f"有完整数据的比赛: {len(complete_data)} 场")
    
    if len(complete_data) < 100:
        logger.error("完整数据不足")
        return
    
    # 训练增强模型
    handicap_line = 2.5
    line_data = complete_data[
        abs(complete_data['goals_line_1'] - handicap_line) < 0.1
    ].copy()
    
    if len(line_data) < 50:
        logger.error(f"盘口 {handicap_line} 数据不足")
        return
    
    logger.info(f"盘口 {handicap_line} 训练数据: {len(line_data)} 场")
    
    # 显示增强特征
    change_features = [col for col in line_data.columns if 'change' in col or 'sentiment' in col]
    if change_features:
        logger.info(f"包含的赔率变化特征: {change_features}")
        
        # 显示特征统计
        for feature in change_features[:5]:  # 只显示前5个
            if feature in line_data.columns:
                if line_data[feature].dtype in ['float64', 'int64']:
                    mean_val = line_data[feature].mean()
                    std_val = line_data[feature].std()
                    logger.info(f"   {feature}: 均值={mean_val:.3f}, 标准差={std_val:.3f}")
                else:
                    value_counts = line_data[feature].value_counts()
                    logger.info(f"   {feature}: {dict(value_counts)}")
    
    try:
        # 训练模型
        model = GoalsModel("lightgbm", "1.0")
        training_result = model.train(line_data, handicap_line)
        
        logger.info(f"增强模型训练结果:")
        logger.info(f"  验证准确率: {training_result['val_accuracy']:.4f}")
        logger.info(f"  交叉验证: {training_result['cv_mean']:.4f} ± {training_result['cv_std']:.4f}")
        
        # 显示特征重要性
        importance_df = model.get_feature_importance()
        if importance_df is not None:
            logger.info(f"特征重要性 (Top 10):")
            for _, row in importance_df.head(10).iterrows():
                feature_type = "🔄" if any(keyword in row['feature'] for keyword in ['change', 'sentiment', 'direction']) else "📊"
                logger.info(f"  {feature_type} {row['feature']}: {row['importance']:.1f}")
        
        return training_result
        
    except Exception as e:
        logger.error(f"增强模型训练失败: {e}")
        return None

def main():
    """主函数"""
    logger.info("开始赔率变化特征测试")
    
    try:
        # 分析赔率变化模式
        analysis_data = analyze_odds_changes_patterns()
        
        # 测试增强模型
        if analysis_data is not None and not analysis_data.empty:
            test_enhanced_model_with_odds_changes()
        
        logger.info("\n✅ 赔率变化特征测试完成")
        
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
