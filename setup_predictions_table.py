#!/usr/bin/env python3
"""
创建预测表和相关结构
"""

import pymysql
import logging

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SetupDB")

def create_predictions_table():
    """创建预测表"""
    
    # 创建预测表的SQL
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS predictions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        match_id INT NOT NULL,
        category VARCHAR(50) NOT NULL COMMENT '预测类别: goals, match_result, asian_handicap, corners',
        handicap_line VARCHAR(20) COMMENT '盘口线',
        predicted_result INT COMMENT '预测结果: 0/1 for binary, 0/1/2 for match_result',
        predicted_probability FLOAT COMMENT '预测概率',
        confidence_level VARCHAR(20) COMMENT '置信度: HIGH, MEDIUM, LOW',
        model_used VARCHAR(200) COMMENT '使用的模型',
        prediction_description TEXT COMMENT '预测描述',
        
        -- 实际结果字段
        actual_result INT COMMENT '实际结果',
        is_correct BOOLEAN COMMENT '预测是否正确',
        verified_at DATETIME COMMENT '验证时间',
        
        -- 元数据
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- 索引
        INDEX idx_match_category (match_id, category),
        INDEX idx_created_at (created_at),
        INDEX idx_verified (is_correct, verified_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预测记录表'
    """
    
    # 创建预测统计视图的SQL
    create_view_sql = """
    CREATE OR REPLACE VIEW prediction_stats AS
    SELECT 
        category,
        confidence_level,
        COUNT(*) as total_predictions,
        SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_predictions,
        AVG(CASE WHEN is_correct = 1 THEN 1.0 ELSE 0.0 END) as accuracy_rate,
        AVG(predicted_probability) as avg_confidence,
        COUNT(CASE WHEN verified_at IS NOT NULL THEN 1 END) as verified_count
    FROM predictions 
    GROUP BY category, confidence_level
    ORDER BY category, confidence_level
    """
    
    try:
        with pymysql.connect(**DB_CONFIG) as conn:
            cursor = conn.cursor()
            
            # 创建预测表
            logger.info("创建预测表...")
            cursor.execute(create_table_sql)
            logger.info("✅ 预测表创建成功")
            
            # 创建统计视图 (先检查表是否有数据)
            logger.info("创建预测统计视图...")
            try:
                cursor.execute(create_view_sql)
                logger.info("✅ 预测统计视图创建成功")
            except Exception as view_error:
                logger.warning(f"⚠️ 统计视图创建失败: {view_error}")
                logger.info("这是正常的，因为表刚创建还没有数据")
            
            conn.commit()
            
            # 检查表结构
            cursor.execute("DESCRIBE predictions")
            columns = cursor.fetchall()
            
            logger.info("📊 预测表结构:")
            for column in columns:
                logger.info(f"  {column[0]}: {column[1]} {column[2] or ''}")
            
            logger.info("🎉 数据库设置完成！")
            
    except Exception as e:
        logger.error(f"❌ 数据库设置失败: {e}")
        raise

if __name__ == "__main__":
    create_predictions_table()
