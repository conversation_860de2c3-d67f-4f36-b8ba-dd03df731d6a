#!/usr/bin/env python3
"""
足球预测模型训练脚本
"""

import pandas as pd
import numpy as np
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List

# 导入自定义模块
from models.data_processor import FootballDataProcessor
from models.goals_model import GoalsModel

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'user': 'root',
    'password': 'admin@89soft',
    'database': 'football_prediction_v3',
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("ModelTraining")

def train_goals_model():
    """训练进球预测模型"""
    logger.info("开始训练进球预测模型")
    
    # 初始化数据处理器
    processor = FootballDataProcessor(DB_CONFIG)
    
    # 获取训练数据 (最近2年的五大联赛和一级赛事数据)
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')
    
    logger.info(f"获取训练数据: {start_date} 到 {end_date}")
    
    # 获取比赛数据
    match_data = processor.get_match_data(
        start_date=start_date,
        end_date=end_date,
        league_priorities=[0, 1],  # 五大联赛和一级赛事
        limit=10000  # 限制数量以便快速测试
    )
    
    if match_data.empty:
        logger.error("没有获取到比赛数据")
        return
    
    logger.info(f"获取到 {len(match_data)} 场比赛数据")
    
    # 获取赔率数据
    match_ids = match_data['match_id'].tolist()
    odds_data = processor.get_odds_data(match_ids, 'OPENING')
    
    logger.info(f"获取到 {len(odds_data)} 条赔率数据")
    
    # 创建特征
    features_data = processor.create_features(match_data, odds_data)
    
    # 过滤有完整数据的比赛
    complete_data = features_data.dropna(subset=['total_goals', 'goals_line_1'])
    
    logger.info(f"有完整数据的比赛: {len(complete_data)} 场")
    
    if len(complete_data) < 100:
        logger.error("训练数据不足")
        return
    
    # 训练不同盘口线的模型
    handicap_lines = [1.5, 2.5, 3.5]  # 常见的进球盘口线
    
    for line in handicap_lines:
        logger.info(f"训练盘口线 {line} 的模型")
        
        # 过滤有该盘口线数据的比赛
        line_data = complete_data[
            (complete_data['goals_line_1'] == line) | 
            (complete_data['goals_line_2'] == line)
        ].copy()
        
        if len(line_data) < 50:
            logger.warning(f"盘口线 {line} 的数据不足: {len(line_data)} 场")
            continue
        
        # 训练LightGBM模型
        model = GoalsModel("lightgbm", "1.0")
        
        try:
            training_result = model.train(line_data, line)
            
            logger.info(f"盘口线 {line} 训练结果:")
            logger.info(f"  训练样本: {training_result['train_samples']}")
            logger.info(f"  验证样本: {training_result['val_samples']}")
            logger.info(f"  验证准确率: {training_result['val_accuracy']:.4f}")
            logger.info(f"  交叉验证: {training_result['cv_mean']:.4f} ± {training_result['cv_std']:.4f}")
            
            # 保存模型
            model_dir = "saved_models"
            os.makedirs(model_dir, exist_ok=True)
            
            model_path = f"{model_dir}/goals_lightgbm_v1.0_line_{line}.joblib"
            model.save_model(model_path)
            
            logger.info(f"模型已保存: {model_path}")
            
            # 显示特征重要性
            importance_df = model.get_feature_importance()
            if importance_df is not None:
                logger.info("特征重要性 (Top 10):")
                for _, row in importance_df.head(10).iterrows():
                    logger.info(f"  {row['feature']}: {row['importance']:.4f}")
            
        except Exception as e:
            logger.error(f"训练盘口线 {line} 的模型失败: {e}")
            continue

def test_model_prediction():
    """测试模型预测"""
    logger.info("测试模型预测")
    
    # 加载模型
    model = GoalsModel("lightgbm", "1.0")
    model_path = "saved_models/goals_lightgbm_v1.0_line_2.5.joblib"
    
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return
    
    if not model.load_model(model_path):
        logger.error("加载模型失败")
        return
    
    # 获取测试数据
    processor = FootballDataProcessor(DB_CONFIG)
    
    # 获取最近的比赛数据
    test_data = processor.get_match_data(
        start_date=(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
        league_priorities=[0, 1],
        limit=10
    )
    
    if test_data.empty:
        logger.warning("没有测试数据")
        return
    
    # 获取赔率数据
    odds_data = processor.get_odds_data(test_data['match_id'].tolist(), 'OPENING')
    
    # 创建特征
    features_data = processor.create_features(test_data, odds_data)
    
    # 进行预测
    try:
        predictions, probabilities = model.predict_for_handicap_line(features_data, 2.5)
        
        logger.info("预测结果:")
        for i, (_, row) in enumerate(features_data.iterrows()):
            pred_text = "大球" if predictions[i] == 1 else "小球"
            prob = probabilities[i]
            
            logger.info(f"  {row['home_team_name']} vs {row['away_team_name']}")
            logger.info(f"    预测: {pred_text} (概率: {prob:.3f})")
            
            if 'total_goals' in row and pd.notna(row['total_goals']):
                actual = "大球" if row['total_goals'] > 2.5 else "小球"
                correct = "正确" if (predictions[i] == 1) == (row['total_goals'] > 2.5) else "错误"
                logger.info(f"    实际: {actual} ({correct})")
    
    except Exception as e:
        logger.error(f"预测失败: {e}")

def main():
    """主函数"""
    logger.info("开始足球预测模型训练")
    
    try:
        # 训练模型
        train_goals_model()
        
        # 测试预测
        test_model_prediction()
        
        logger.info("模型训练完成")
        
    except Exception as e:
        logger.error(f"训练过程出错: {e}")
        raise

if __name__ == "__main__":
    main()
