-- =====================================================
-- 足球预测系统 V3.0 数据库架构
-- 微服务架构 + 动态盘口 + 红牌计算
-- =====================================================

CREATE DATABASE IF NOT EXISTS football_prediction_v3 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE football_prediction_v3;

-- =====================================================
-- 1. 联赛表 (leagues)
-- =====================================================
CREATE TABLE leagues (
    id INT PRIMARY KEY COMMENT '联赛ID',
    code VARCHAR(10) NOT NULL UNIQUE COMMENT '联赛代码 E0=英超',
    name VARCHAR(100) NOT NULL COMMENT '联赛中文名称',
    full_name VARCHAR(200) COMMENT '联赛中文名称全称',
    english_name VARCHAR(100) COMMENT '联赛英文名称',
    logo_url VARCHAR(500) COMMENT '联赛logo图片地址',
    country VARCHAR(50) COMMENT '国家',
    country_id INT COMMENT '国家ID',
    country_logo_url VARCHAR(500) COMMENT '国家logo图片地址',
    season_id INT COMMENT '当前赛季ID',
    priority TINYINT NOT NULL DEFAULT 3 COMMENT '0=五大联赛 1=一级赛事 2-98=其他赛事 99=杯赛',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_code (code),
    INDEX idx_priority (priority),
    INDEX idx_active (is_active),
    INDEX idx_country (country),
    INDEX idx_country_id (country_id),
    INDEX idx_season_id (season_id)
) COMMENT '联赛信息表';

-- =====================================================
-- 2. 球队表 (teams)
-- =====================================================
CREATE TABLE teams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '球队中文名称',
    english_name VARCHAR(100) COMMENT '球队英文名称',
    short_name VARCHAR(50) COMMENT '简称',
    league_id INT NOT NULL COMMENT '所属联赛',
    country VARCHAR(50) COMMENT '国家',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_name (name),
    INDEX idx_league (league_id),
    INDEX idx_active (is_active),
    INDEX idx_country (country)
) COMMENT '球队信息表';

-- =====================================================
-- 3. 比赛表 (matches)
-- =====================================================
CREATE TABLE matches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    external_id VARCHAR(100) COMMENT '外部数据源ID',
    league_id INT NOT NULL COMMENT '联赛ID',
    season VARCHAR(10) COMMENT '赛季 2024-25',
    season_id INT COMMENT '赛季ID',
    home_team_id INT COMMENT '主队ID',
    away_team_id INT COMMENT '客队ID',
    home_team_name VARCHAR(100) COMMENT '主队名称',
    away_team_name VARCHAR(100) COMMENT '客队名称',
    home_team_logo_url VARCHAR(500) COMMENT '主队logo图片地址',
    away_team_logo_url VARCHAR(500) COMMENT '客队logo图片地址',
    match_date DATE NOT NULL COMMENT '比赛日期',
    match_time TIME COMMENT '比赛时间',
    match_datetime DATETIME GENERATED ALWAYS AS (
        CASE
            WHEN match_time IS NOT NULL THEN TIMESTAMP(match_date, match_time)
            ELSE TIMESTAMP(match_date, '00:00:00')
        END
    ) STORED COMMENT '完整比赛时间',
    status ENUM('SCHEDULED', 'LIVE', 'FINISHED', 'POSTPONED', 'CANCELLED')
           DEFAULT 'SCHEDULED' COMMENT '比赛状态',
    round_number TINYINT COMMENT '轮次',
    round varchar(100)  COMMENT '轮次名称',
    venue VARCHAR(100) COMMENT '比赛场地',
    data_source VARCHAR(50) COMMENT '数据来源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_external_match (external_id, data_source),
    INDEX idx_match_date (match_date),
    INDEX idx_match_datetime (match_datetime),
    INDEX idx_league_season (league_id, season),
    INDEX idx_season_id (season_id),
    INDEX idx_teams (home_team_id, away_team_id),
    INDEX idx_team_names (home_team_name, away_team_name),
    INDEX idx_status (status)
) COMMENT '比赛基础信息表';

-- =====================================================
-- 4. 比赛统计表 (match_stats)
-- 核心统计数据，用于预测验证
-- =====================================================
CREATE TABLE match_stats (
    match_id INT PRIMARY KEY COMMENT '比赛ID',
    
    -- 进球数据
    home_goals INT DEFAULT 0 COMMENT '主队进球',
    away_goals INT DEFAULT 0 COMMENT '客队进球',
    total_goals INT GENERATED ALWAYS AS (home_goals + away_goals) STORED COMMENT '总进球',
    
    -- 半场数据
    home_goals_ht INT DEFAULT 0 COMMENT '主队半场进球',
    away_goals_ht INT DEFAULT 0 COMMENT '客队半场进球',
    total_goals_ht INT GENERATED ALWAYS AS (home_goals_ht + away_goals_ht) STORED COMMENT '半场总进球',
    
    -- 角球数据
    home_corners INT DEFAULT 0 COMMENT '主队角球',
    away_corners INT DEFAULT 0 COMMENT '客队角球',
    total_corners INT GENERATED ALWAYS AS (home_corners + away_corners) STORED COMMENT '总角球',
    
    -- 黄牌数据
    home_yellow_cards INT DEFAULT 0 COMMENT '主队黄牌',
    away_yellow_cards INT DEFAULT 0 COMMENT '客队黄牌',
    total_yellow_cards INT GENERATED ALWAYS AS (home_yellow_cards + away_yellow_cards) STORED COMMENT '总黄牌',
    
    -- 红牌数据
    home_red_cards INT DEFAULT 0 COMMENT '主队红牌',
    away_red_cards INT DEFAULT 0 COMMENT '客队红牌',
    total_red_cards INT GENERATED ALWAYS AS (home_red_cards + away_red_cards) STORED COMMENT '总红牌',
    
    -- 综合牌数 (红牌=2黄牌)
    home_total_cards INT GENERATED ALWAYS AS (home_yellow_cards + home_red_cards * 2) STORED COMMENT '主队总牌数',
    away_total_cards INT GENERATED ALWAYS AS (away_yellow_cards + away_red_cards * 2) STORED COMMENT '客队总牌数',
    total_cards INT GENERATED ALWAYS AS (total_yellow_cards + total_red_cards * 2) STORED COMMENT '总牌数',
    
    -- 比赛结果
    result ENUM('H', 'D', 'A') COMMENT '比赛结果 H=主胜 D=平局 A=客胜',
    result_ht ENUM('H', 'D', 'A') COMMENT '半场结果',
    
    -- 射门统计
    home_shots INT COMMENT '主队射门',
    away_shots INT COMMENT '客队射门',
    home_shots_on_target INT COMMENT '主队射正',
    away_shots_on_target INT COMMENT '客队射正',
    home_shots_off_target INT COMMENT '主队射偏',
    away_shots_off_target INT COMMENT '客队射偏',
    home_shots_blocked INT COMMENT '主队射门被封',
    away_shots_blocked INT COMMENT '客队射门被封',
    home_shots_inside_box INT COMMENT '主队禁区内射门',
    away_shots_inside_box INT COMMENT '客队禁区内射门',
    home_shots_outside_box INT COMMENT '主队禁区外射门',
    away_shots_outside_box INT COMMENT '客队禁区外射门',

    -- 控球和传球统计
    home_possession DECIMAL(5,2) COMMENT '主队控球率%',
    away_possession DECIMAL(5,2) COMMENT '客队控球率%',
    home_passes INT COMMENT '主队传球次数',
    away_passes INT COMMENT '客队传球次数',
    home_passes_accurate INT COMMENT '主队准确传球',
    away_passes_accurate INT COMMENT '客队准确传球',
    home_long_passes INT COMMENT '主队长传次数',
    away_long_passes INT COMMENT '客队长传次数',
    home_long_passes_accurate INT COMMENT '主队长传成功次数',
    away_long_passes_accurate INT COMMENT '客队长传成功次数',
    home_crosses INT COMMENT '主队传中次数',
    away_crosses INT COMMENT '客队传中次数',
    home_crosses_accurate INT COMMENT '主队传中成功次数',
    away_crosses_accurate INT COMMENT '客队传中成功次数',

    -- 犯规和纪律统计
    home_fouls TINYINT COMMENT '主队犯规',
    away_fouls TINYINT COMMENT '客队犯规',

    -- 定位球统计
    home_free_kicks TINYINT COMMENT '主队任意球',
    away_free_kicks TINYINT COMMENT '客队任意球',
    home_goal_kicks TINYINT COMMENT '主队球门球',
    away_goal_kicks TINYINT COMMENT '客队球门球',
    home_throw_ins TINYINT COMMENT '主队界外球',
    away_throw_ins TINYINT COMMENT '客队界外球',

    -- 门将统计
    home_saves TINYINT COMMENT '主队扑救',
    away_saves TINYINT COMMENT '客队扑救',
    home_safe_passes DECIMAL(5,2) COMMENT '主队安全球率%',
    away_safe_passes DECIMAL(5,2) COMMENT '客队安全球率%',

    -- 进攻统计
    home_attacks TINYINT COMMENT '主队进攻',
    away_attacks TINYINT COMMENT '客队进攻',
    home_dangerous_attacks TINYINT COMMENT '主队危险进攻',
    away_dangerous_attacks TINYINT COMMENT '客队危险进攻',
    home_scoring_chances TINYINT COMMENT '主队得分机会',
    away_scoring_chances TINYINT COMMENT '客队得分机会',

    -- 防守统计
    home_tackles TINYINT COMMENT '主队铲球次数',
    away_tackles TINYINT COMMENT '客队铲球次数',
    home_interceptions TINYINT COMMENT '主队拦截',
    away_interceptions TINYINT COMMENT '客队拦截',
    home_clearances TINYINT COMMENT '主队解围',
    away_clearances TINYINT COMMENT '客队解围',

    -- 技术统计
    home_dribbles TINYINT COMMENT '主队盘带次数',
    away_dribbles TINYINT COMMENT '客队盘带次数',
    home_dribbles_successful TINYINT COMMENT '主队盘带成功次数',
    away_dribbles_successful TINYINT COMMENT '客队盘带成功次数',
    home_offsides TINYINT COMMENT '主队越位',
    away_offsides TINYINT COMMENT '客队越位',

    -- 点球统计
    home_penalties TINYINT COMMENT '主队点球',
    away_penalties TINYINT COMMENT '客队点球',
    home_penalties_scored TINYINT COMMENT '主队点球进球',
    away_penalties_scored TINYINT COMMENT '客队点球进球',

    -- 时间统计
    injury_time_first_half TINYINT COMMENT '上半场伤停时间',
    injury_time_second_half TINYINT COMMENT '下半场伤停时间',

    -- 数据完整性标记
    has_basic_stats BOOLEAN DEFAULT FALSE COMMENT '是否有基础统计',
    has_extended_stats BOOLEAN DEFAULT FALSE COMMENT '是否有扩展统计',
    has_advanced_stats BOOLEAN DEFAULT FALSE COMMENT '是否有高级统计',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    

    
    INDEX idx_result (result),
    INDEX idx_total_goals (total_goals),
    INDEX idx_total_corners (total_corners),
    INDEX idx_total_cards (total_cards),
    INDEX idx_basic_stats (has_basic_stats)
) COMMENT '比赛统计数据表';

-- =====================================================
-- 5. 盘口配置表 (handicap_configs)
-- 动态盘口管理
-- =====================================================
CREATE TABLE handicap_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category ENUM('GOALS', 'CORNERS', 'CARDS', 'RESULT', 'ASIAN_HANDICAP') NOT NULL COMMENT '预测类别',
    display_name VARCHAR(50) NOT NULL COMMENT '显示名称 如大球、角球、黄牌、胜平负、亚盘',
    description TEXT COMMENT '详细描述',
    is_popular BOOLEAN DEFAULT FALSE COMMENT '是否热门预测类别',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order TINYINT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_category (category),
    INDEX idx_category (category),
    INDEX idx_popular (is_popular),
    INDEX idx_active (is_active)
) COMMENT '公共盘口配置表';

-- =====================================================
-- 6. 预测表 (predictions)
-- 存储所有预测结果
-- =====================================================
CREATE TABLE predictions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    match_id INT NOT NULL COMMENT '比赛ID',
    handicap_config_id INT NOT NULL COMMENT '预测类别配置ID',
    handicap_line DECIMAL(4,2) COMMENT '具体盘口线 从odds表获取',
    model_type ENUM('LONG_TERM', 'CURRENT_SEASON', 'UNIVERSAL') NOT NULL COMMENT '模型类型',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    model_version VARCHAR(20) COMMENT '模型版本',
    
    -- 预测结果
    predicted_value TINYINT NOT NULL COMMENT '预测值 0=否 1=是',
    predicted_probability DECIMAL(6,4) NOT NULL COMMENT '预测概率 0-1',
    confidence_level ENUM('HIGH', 'MEDIUM', 'LOW') NOT NULL COMMENT '置信度',
    
    -- 验证结果
    actual_value DECIMAL(5,2) COMMENT '实际数值',
    actual_result TINYINT COMMENT '实际结果 0=否 1=是',
    is_correct BOOLEAN COMMENT '是否预测正确',
    
    -- 时间信息
    prediction_time DATETIME NOT NULL COMMENT '预测时间',
    verification_time DATETIME COMMENT '验证时间',
    
    -- 模型信息
    model_accuracy DECIMAL(6,4) COMMENT '模型历史准确率',
    feature_importance JSON COMMENT '特征重要性',
    prediction_metadata JSON COMMENT '预测元数据',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_match_model_handicap (match_id, model_type, handicap_config_id),
    INDEX idx_model_type (model_type),
    INDEX idx_confidence (confidence_level),
    INDEX idx_accuracy (is_correct),
    INDEX idx_prediction_time (prediction_time),
    INDEX idx_verification_time (verification_time),
    INDEX idx_handicap_config (handicap_config_id)
) COMMENT '预测结果表';

-- =====================================================
-- 7. 赔率表 (odds)
-- 存储初盘和即时赔率
-- =====================================================
CREATE TABLE odds (
    match_id INT NOT NULL COMMENT '比赛ID',
    bookmaker VARCHAR(50) NOT NULL COMMENT '博彩公司',
    odds_type ENUM('OPENING', 'LIVE', 'CLOSING') NOT NULL COMMENT '赔率类型 OPENING=初盘 LIVE=比赛开始时 CLOSING=封盘',
    
    -- 胜平负赔率
    home_win_odds DECIMAL(6,3) COMMENT '主胜赔率',
    draw_odds DECIMAL(6,3) COMMENT '平局赔率',
    away_win_odds DECIMAL(6,3) COMMENT '客胜赔率',
    
    -- 大小球赔率 (支持多个盘口)
    goals_line_1 DECIMAL(4,2) COMMENT '进球盘口1',
    goals_over_odds_1 DECIMAL(6,3) COMMENT '大球赔率1',
    goals_under_odds_1 DECIMAL(6,3) COMMENT '小球赔率1',
    
    goals_line_2 DECIMAL(4,2) COMMENT '进球盘口2',
    goals_over_odds_2 DECIMAL(6,3) COMMENT '大球赔率2',
    goals_under_odds_2 DECIMAL(6,3) COMMENT '小球赔率2',
    
    goals_line_3 DECIMAL(4,2) COMMENT '进球盘口3',
    goals_over_odds_3 DECIMAL(6,3) COMMENT '大球赔率3',
    goals_under_odds_3 DECIMAL(6,3) COMMENT '小球赔率3',
    
    -- 角球赔率
    corners_line DECIMAL(4,2) COMMENT '角球盘口',
    corners_over_odds DECIMAL(6,3) COMMENT '角球大赔率',
    corners_under_odds DECIMAL(6,3) COMMENT '角球小赔率',
    
    -- 牌数赔率
    cards_line DECIMAL(4,2) COMMENT '牌数盘口',
    cards_over_odds DECIMAL(6,3) COMMENT '牌数大赔率',
    cards_under_odds DECIMAL(6,3) COMMENT '牌数小赔率',

    -- 亚洲盘口 (让球盘)
    asian_handicap_line_1 DECIMAL(4,2) COMMENT '亚盘盘口1 如-0.5, +1.25',
    asian_home_odds_1 DECIMAL(6,3) COMMENT '亚盘主队赔率1',
    asian_away_odds_1 DECIMAL(6,3) COMMENT '亚盘客队赔率1',

    asian_handicap_line_2 DECIMAL(4,2) COMMENT '亚盘盘口2',
    asian_home_odds_2 DECIMAL(6,3) COMMENT '亚盘主队赔率2',
    asian_away_odds_2 DECIMAL(6,3) COMMENT '亚盘客队赔率2',

    asian_handicap_line_3 DECIMAL(4,2) COMMENT '亚盘盘口3',
    asian_home_odds_3 DECIMAL(6,3) COMMENT '亚盘主队赔率3',
    asian_away_odds_3 DECIMAL(6,3) COMMENT '亚盘客队赔率3',

    -- 时间和来源信息
    odds_time DATETIME COMMENT '赔率时间',
    data_source VARCHAR(50) COMMENT '数据来源',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_match_bookmaker (match_id, bookmaker),
    INDEX idx_odds_type (odds_type),
    INDEX idx_odds_time (odds_time),
    PRIMARY KEY (match_id, bookmaker, odds_type),
    INDEX idx_match_id (match_id),
    INDEX idx_bookmaker (bookmaker),
    INDEX idx_odds_type (odds_type),
    INDEX idx_data_source (data_source)
) COMMENT '赔率数据表';

-- =====================================================
-- 8. 模型性能表 (model_performance)
-- =====================================================
CREATE TABLE model_performance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    model_type ENUM('LONG_TERM', 'CURRENT_SEASON', 'UNIVERSAL') NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    league_id INT COMMENT '联赛ID NULL=全联赛',
    handicap_config_id INT NOT NULL COMMENT '盘口配置ID',
    
    -- 统计周期
    stats_date DATE NOT NULL COMMENT '统计日期',
    period_days INT DEFAULT 30 COMMENT '统计周期天数',
    
    -- 性能指标
    total_predictions INT DEFAULT 0 COMMENT '总预测数',
    correct_predictions INT DEFAULT 0 COMMENT '正确预测数',
    success_rate DECIMAL(6,4) COMMENT '成功率',
    avg_probability DECIMAL(6,4) COMMENT '平均预测概率',
    
    -- 置信度分析
    high_confidence_total INT DEFAULT 0 COMMENT '高置信度总数',
    high_confidence_correct INT DEFAULT 0 COMMENT '高置信度正确数',
    high_confidence_rate DECIMAL(6,4) COMMENT '高置信度成功率',
    
    -- 趋势分析
    trend_direction ENUM('UP', 'DOWN', 'STABLE') COMMENT '趋势方向',
    trend_strength DECIMAL(4,2) COMMENT '趋势强度',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_model_league_handicap_date (model_type, league_id, handicap_config_id, stats_date),
    INDEX idx_model_performance (model_type, handicap_config_id),
    INDEX idx_success_rate (success_rate),
    INDEX idx_stats_date (stats_date)
) COMMENT '模型性能统计表';

-- =====================================================
-- 9. 系统配置表 (system_config)
-- =====================================================
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('STRING', 'INTEGER', 'FLOAT', 'BOOLEAN', 'JSON') DEFAULT 'STRING',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key),
    INDEX idx_active (is_active)
) COMMENT '系统配置表';

-- =====================================================
-- 10. 赛季表 (season)
-- =====================================================
CREATE TABLE season (
    season_id INT NOT NULL PRIMARY KEY COMMENT '赛季ID',
    season_name VARCHAR(255) COMMENT '赛季名称',
    gameid INT COMMENT '联赛ID (实际为联赛ID)',
    sort DECIMAL(10,0) COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_gameid (gameid),
    INDEX idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '赛季表';

-- =====================================================
-- 11. 赛季分组表 (season_group)
-- =====================================================
CREATE TABLE season_group (
    group_id INT NOT NULL PRIMARY KEY COMMENT '分组ID',
    group_name VARCHAR(255) COMMENT '分组名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_group_name (group_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '赛季分组表';

-- =====================================================
-- 12. 轮次表 (season_round)
-- =====================================================
CREATE TABLE season_round (
    season_id INT NOT NULL COMMENT '赛季ID',
    season_name VARCHAR(255) COMMENT '赛季名称',
    group_id VARCHAR(150) NOT NULL COMMENT '分组ID',
    group_name VARCHAR(255) COMMENT '分组名称',
    round VARCHAR(50) NOT NULL COMMENT '轮次',
    sort DECIMAL(10,2) COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (season_id, group_id, round),
    UNIQUE KEY uk_season_group_round (season_id, group_id, round),
    INDEX idx_season_id (season_id),
    INDEX idx_group_id (group_id),
    INDEX idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '轮次表';

-- =====================================================
-- 13. 用户表 (users) - 微服务认证
-- =====================================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('ADMIN', 'USER', 'VIEWER') DEFAULT 'USER' COMMENT '用户角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login DATETIME COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
) COMMENT '用户表';

-- =====================================================
-- 创建视图
-- =====================================================

-- 比赛详情视图
CREATE VIEW v_match_details AS
SELECT
    m.id,
    m.external_id,
    l.code as league_code,
    l.name as league_name,
    l.english_name as league_english_name,
    l.country as league_country,
    m.season,
    m.season_id,
    m.home_team_id,
    m.away_team_id,
    m.home_team_name as home_team,
    m.away_team_name as away_team,
    m.home_team_logo_url,
    m.away_team_logo_url,
    m.match_date,
    m.match_time,
    m.match_datetime,
    m.status,
    m.round_number,
    m.venue,
    ms.home_goals,
    ms.away_goals,
    ms.total_goals,
    ms.total_corners,
    ms.total_yellow_cards,
    ms.total_red_cards,
    ms.total_cards,
    ms.result,
    ms.home_shots,
    ms.away_shots,
    ms.home_shots_on_target,
    ms.away_shots_on_target,
    ms.home_possession,
    ms.away_possession,
    ms.has_basic_stats,
    ms.has_extended_stats,
    ms.has_advanced_stats
FROM matches m
JOIN leagues l ON m.league_id = l.id
LEFT JOIN match_stats ms ON m.id = ms.match_id;

-- 预测准确率视图
CREATE VIEW v_prediction_accuracy AS
SELECT 
    p.model_type,
    hc.category,
    hc.display_name,
    l.name as league_name,
    COUNT(*) as total_predictions,
    SUM(CASE WHEN p.is_correct = 1 THEN 1 ELSE 0 END) as correct_predictions,
    AVG(CASE WHEN p.is_correct IS NOT NULL THEN p.is_correct ELSE NULL END) as success_rate,
    AVG(p.predicted_probability) as avg_probability,
    COUNT(CASE WHEN p.confidence_level = 'HIGH' THEN 1 END) as high_confidence_count
FROM predictions p
JOIN handicap_configs hc ON p.handicap_config_id = hc.id
JOIN matches m ON p.match_id = m.id
JOIN leagues l ON m.league_id = l.id
WHERE p.actual_result IS NOT NULL
GROUP BY p.model_type, hc.category, hc.display_name, l.name;

-- 热门盘口视图
CREATE VIEW v_popular_handicaps AS
SELECT
    hc.*,
    COUNT(p.id) as prediction_count,
    AVG(CASE WHEN p.is_correct IS NOT NULL THEN p.is_correct ELSE NULL END) as avg_success_rate
FROM handicap_configs hc
LEFT JOIN predictions p ON hc.id = p.handicap_config_id
WHERE hc.is_popular = TRUE AND hc.is_active = TRUE
GROUP BY hc.id
ORDER BY hc.category, hc.sort_order;

-- =====================================================
-- 插入基础数据
-- =====================================================

-- 联赛数据将通过应用程序插入，此处不包含示例数据

-- 盘口配置数据将通过应用程序插入，此处不包含示例数据

-- 系统配置和用户数据将通过应用程序插入，此处不包含示例数据

-- 存储过程将通过应用程序创建，此处暂时跳过以避免语法问题

-- 触发器将通过应用程序创建，此处暂时跳过

-- =====================================================
-- 创建索引优化
-- =====================================================

-- 复合索引优化查询性能
CREATE INDEX idx_matches_league_date_status ON matches(league_id, match_date, status);
CREATE INDEX idx_predictions_model_time_verified ON predictions(model_type, prediction_time, verification_time);
CREATE INDEX idx_stats_totals_result ON match_stats(total_goals, total_corners, total_cards, result);
CREATE INDEX idx_odds_match_type_time ON odds(match_id, odds_type, odds_time);

-- =====================================================
-- 完成
-- =====================================================

SELECT 'Football Prediction V3.0 Database Created Successfully!' as status;
SELECT COUNT(*) as leagues_count FROM leagues;
SELECT COUNT(*) as handicap_configs_count FROM handicap_configs;
SELECT COUNT(*) as system_configs_count FROM system_config;
